# 企业微信接收消息地址验证配置指南

## 概述

本项目已实现企业微信接收消息地址验证功能，回调地址为 `/wechat/callback`。

## 配置步骤

### 1. 环境变量配置

在 `.env` 文件中添加以下企业微信相关配置：

```env
# 企业微信配置
WECHAT_CORP_ID=你的企业ID
WECHAT_CORP_SECRET=你的应用密钥
WECHAT_AGENT_ID=你的应用ID
WECHAT_TOKEN=你的回调Token
WECHAT_ENCODING_AES_KEY=你的数据加密密钥
WECHAT_LOG_ENABLED=true
WECHAT_LOG_LEVEL=info
```

### 2. 企业微信管理后台配置

1. 登录企业微信管理后台
2. 进入应用管理 -> 选择你的应用
3. 在"接收消息"设置中：
   - **URL**: `https://你的域名/wechat/callback`
   - **Token**: 填入你设置的WECHAT_TOKEN值
   - **EncodingAESKey**: 填入你设置的WECHAT_ENCODING_AES_KEY值

### 3. 验证流程

当你在企业微信后台保存配置时，企业微信会向你的回调URL发送GET请求进行验证：

```
GET /wechat/callback?msg_signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx
```

系统会自动：
1. 验证请求签名的有效性
2. 如果验证成功，返回echostr参数
3. 记录验证过程的日志

## 技术实现

### 签名验证算法

1. 将token、timestamp、nonce、echostr按字典序排序
2. 拼接成字符串
3. 进行SHA1加密
4. 与msg_signature参数比较

### 文件结构

- `app/Http/Controllers/WeChatController.php` - 企业微信回调控制器
- `config/wechat.php` - 企业微信配置文件
- `routes/web.php` - 路由配置

### 日志记录

系统会记录以下信息：
- 回调请求参数
- 签名验证过程
- 验证结果

日志文件位置：`storage/logs/laravel.log`

## 故障排除

### 常见问题

1. **Token未配置**
   - 检查.env文件中WECHAT_TOKEN是否设置
   - 确保配置缓存已清除：`php artisan config:clear`

2. **签名验证失败**
   - 检查Token是否与企业微信后台配置一致
   - 查看日志文件确认计算的签名值

3. **URL无法访问**
   - 确保域名可以从外网访问
   - 检查防火墙和服务器配置
   - 确保使用HTTPS协议

### 调试命令

```bash
# 清除配置缓存
php artisan config:clear

# 查看日志
tail -f storage/logs/laravel.log

# 测试路由
php artisan route:list | grep wechat
```

## 安全注意事项

1. 确保Token足够复杂，建议使用随机生成的字符串
2. 定期更换Token和EncodingAESKey
3. 监控日志文件，及时发现异常访问
4. 使用HTTPS协议保护数据传输

## 独立版本使用

如果你遇到Laravel框架相关问题，可以使用独立版本：

### 配置独立版本

1. 编辑 `public/wechat_callback_standalone.php` 文件
2. 修改配置参数：
   ```php
   $config = [
       'token' => '你的企业微信Token', // 替换为实际Token
       'log_enabled' => true,
       'log_file' => __DIR__ . '/../storage/logs/wechat_callback.log'
   ];
   ```

### 使用独立版本

在企业微信管理后台配置：
- **URL**: `https://你的域名/wechat_callback_standalone.php`
- **Token**: 与配置文件中的token保持一致

### 独立版本特点

- 不依赖Laravel框架
- 可以独立运行
- 包含完整的日志记录功能
- 支持URL验证和消息接收

## 下一步

配置完成后，你可以：
1. 扩展消息处理逻辑
2. 实现具体的业务功能
3. 添加消息加解密功能
4. 集成更多企业微信API
