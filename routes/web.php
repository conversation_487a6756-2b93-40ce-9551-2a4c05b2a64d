<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('index');
})->name('index');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/services', function () {
    return view('services');
})->name('services');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

// 企业微信回调路由
Route::match(['get', 'post'], '/wechat/callback', [App\Http\Controllers\WeChatController::class, 'callback'])
    ->name('wechat.callback');

