<?php

/**
 * 企业微信回调URL验证 - 独立版本
 * 
 * 这个文件可以独立运行，不依赖Laravel框架
 * 用于企业微信接收消息地址验证
 * 
 * 访问地址: https://你的域名/wechat_callback_standalone.php
 */

// 配置参数 - 请根据实际情况修改
$config = [
    'token' => 'your_wechat_token_here', // 请替换为你的企业微信Token
    'log_enabled' => true,
    'log_file' => __DIR__ . '/../storage/logs/wechat_callback.log'
];

/**
 * 记录日志
 */
function writeLog($message, $data = []) {
    global $config;
    
    if (!$config['log_enabled']) {
        return;
    }
    
    $logDir = dirname($config['log_file']);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}";
    
    if (!empty($data)) {
        $logMessage .= " " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    $logMessage .= "\n";
    
    file_put_contents($config['log_file'], $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * 验证签名
 */
function verifySignature($token, $timestamp, $nonce, $echostr, $msg_signature) {
    // 将token、timestamp、nonce、echostr按字典序排序
    $array = [$token, $timestamp, $nonce, $echostr];
    sort($array, SORT_STRING);
    
    // 拼接字符串
    $str = implode('', $array);
    
    // 进行sha1加密
    $signature = sha1($str);
    
    writeLog('签名验证', [
        'calculated_signature' => $signature,
        'received_signature' => $msg_signature,
        'sorted_array' => $array
    ]);
    
    // 比较签名
    return $signature === $msg_signature;
}

/**
 * 处理企业微信回调
 */
function handleWeChatCallback() {
    global $config;
    
    // 获取请求参数
    $msg_signature = $_GET['msg_signature'] ?? '';
    $timestamp = $_GET['timestamp'] ?? '';
    $nonce = $_GET['nonce'] ?? '';
    $echostr = $_GET['echostr'] ?? '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    writeLog('企业微信回调请求', [
        'method' => $method,
        'msg_signature' => $msg_signature,
        'timestamp' => $timestamp,
        'nonce' => $nonce,
        'echostr' => $echostr,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? ''
    ]);
    
    // 检查Token配置
    if (empty($config['token']) || $config['token'] === 'your_wechat_token_here') {
        writeLog('错误: Token未配置');
        http_response_code(500);
        echo 'Token not configured';
        return;
    }
    
    // 检查必要参数
    if (empty($msg_signature) || empty($timestamp) || empty($nonce)) {
        writeLog('错误: 缺少必要参数');
        http_response_code(400);
        echo 'Missing required parameters';
        return;
    }
    
    // GET请求 - URL验证
    if ($method === 'GET') {
        if (empty($echostr)) {
            writeLog('错误: GET请求缺少echostr参数');
            http_response_code(400);
            echo 'Missing echostr parameter';
            return;
        }
        
        // 验证签名
        if (verifySignature($config['token'], $timestamp, $nonce, $echostr, $msg_signature)) {
            writeLog('URL验证成功');
            echo $echostr;
        } else {
            writeLog('URL验证失败: 签名不匹配');
            http_response_code(403);
            echo 'Signature verification failed';
        }
        return;
    }
    
    // POST请求 - 消息推送
    if ($method === 'POST') {
        $postData = file_get_contents('php://input');
        writeLog('收到POST消息', [
            'content_length' => strlen($postData),
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? '',
            'body_preview' => substr($postData, 0, 200)
        ]);
        
        // TODO: 实现消息处理逻辑
        echo 'success';
        return;
    }
    
    // 其他请求方法
    writeLog('不支持的请求方法: ' . $method);
    http_response_code(405);
    echo 'Method not allowed';
}

// 设置响应头
header('Content-Type: text/plain; charset=utf-8');

// 处理回调
try {
    handleWeChatCallback();
} catch (Exception $e) {
    writeLog('异常: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(500);
    echo 'Internal server error';
}

?>
