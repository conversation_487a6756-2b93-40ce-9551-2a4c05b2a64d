

$.ajax({
    type: "GET",
    url: "./ir.json",
    dataType: "json",
    cache: false,
    success: function (data) {
        data.forEach((item, index) => {
            let download = ''
            if (item.download) {
                download += item.download.htm ? `<a class="htm" href="${item.download.htm}"><img src="../images/_htm.gif" alt=""></a>` : '';
                download += item.download.pdf ? `<a class="pdf" href="${item.download.pdf}"><img src="../images/_pdf.gif" alt=""></a>` : '';
                download += item.download.doc ? `<a class="doc" href="${item.download.doc}"><img src="../images/_doc.gif" alt=""></a>` : '';
                download += item.download.zip ? `<a class="zip" href="${item.download.zip}"><img src="../images/_zip.gif" alt=""></a>` : '';
                download += item.download.xml ? `<a class="xml" href="${item.download.xml}"><img src="../images/_xml.gif" alt=""></a>` : '';
            }
            bgClass = !(index % 2) ? 'bg-slate-400' : '' ;
            $('.json-table').append(`
                            <div class="flex w-full items-center p-2 ${bgClass}">
                                <div class="w-40">${item.date}</div>
                                <div class="w-20">${item.form}</div>
                                <div class="flex-auto">${item.des}</div>
                                <div class="w-40 flex items-center gap-1">
                                    ${download}
                                </div>
                            </div>`);
        });
    }
});
