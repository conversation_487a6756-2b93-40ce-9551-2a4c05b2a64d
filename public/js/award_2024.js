// 注册 ScrollTrigger 插件
gsap.registerPlugin(ScrollTrigger);
runawardjs();
// 等待 DOM 加载完成
function runawardjs(){

  // document.addEventListener("DOMContentLoaded", () => {
    document.addEventListener('scroll', () => {
      parallaxScroll();
    });
  
    function parallaxScroll(){
      var scrolled = window.pageYOffset || document.documentElement.scrollTop;
      $('.bg-image').css('background-position-y',((scrolled*.005))+'%');
    }
    // 获取所有 awards-icon-container 元素
    const containers = document.querySelectorAll(".awards-icon-container");
  
    containers.forEach((container, containerIndex) => {
      const icons = container.querySelectorAll(".award-icon");
  
      // 为每个容器创建时间轴
      const stage1Tl = gsap.timeline({
        scrollTrigger: {
          trigger: container,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
          // toggleActions: "play reverse restart reverse",
          markers: false,
          id: `stage1_${containerIndex}`,
        },
      });
  
      // 扇形展开效果
      stage1Tl
        .from(container, {
          opacity: 1,
          scale: 0.8,
          duration: 0.3,
        })
        .from(
          icons,
          {
            opacity: 0,
            scale: 0,
            rotation: -120,
            x: 50,
            stagger: {
              each: 0.15,
              from: "start",
              ease: "power2.inOut",
            },
            duration: 1,
            ease: "back.out(1)",
          },
        );
    });
  
    // 获取所有 asiainfo-awards 元素
    const awardsSections = document.querySelectorAll(".asiainfo-awards");
  
    awardsSections.forEach((section, sectionIndex) => {
      // 为每个 section 创建时间轴
      const awardsTl = gsap.timeline({
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
          markers: false,
          id: `awards_${sectionIndex}`,
        },
      });
  
      // 简单画
      awardsTl.from(section.querySelectorAll("img"), {
        opacity: 0,
        y: 30,
        stagger: {
          amount: 0,
          from: "center",
          ease: "power2.out",
        },
        duration: 1,
        ease: "power2.out",
      });
  
      // 检查是否为移动设备
      const isMobile = window.innerWidth <= 768;
  
      // 只在非移动设备上添加鼠标跟随效果
      if (!isMobile) {
        // 添加鼠标跟随效果
        section.addEventListener("mousemove", (e) => {
          const images = section.querySelectorAll("img");
          const rect = section.getBoundingClientRect();
          const mouseX = e.clientX - rect.left;
          const mouseY = e.clientY - rect.top;
  
          images.forEach((img) => {
            const imgRect = img.getBoundingClientRect();
            const imgX = imgRect.left - rect.left + imgRect.width / 2;
            const imgY = imgRect.top - rect.top + imgRect.height / 2;
  
            const deltaX = mouseX - imgX;
            const deltaY = mouseY - imgY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const maxDistance = Math.sqrt(
              rect.width * rect.width + rect.height * rect.height
            );
            const intensity = 1 - Math.min(distance / maxDistance, 1);
  
            gsap.to(img, {
              x: -deltaX * 0.05 * intensity,
              y: -deltaY * 0.05 * intensity,
              rotation: -deltaX * 0.02 * intensity,
              scale: 1 + 0.1 * intensity,
              duration: 0.3,
              ease: "power2.out",
            });
          });
        });
  
        section.addEventListener("mouseleave", () => {
          const images = section.querySelectorAll("img");
          images.forEach((img) => {
            gsap.to(img, {
              x: 0,
              y: 0,
              rotation: 0,
              scale: 1,
              duration: 0.5,
              ease: "power2.out",
            });
          });
        });
      }
    });
  
    // 获取所有 trans-section 元素
    const transSections = document.querySelectorAll(".trans-section");
    transSections.forEach((section, index) => {
      // 为个 section 创建时间轴
      const sectionTl = gsap.timeline({
        scrollTrigger: {
          trigger: section,
          start: "top 80%", // 当section顶部到达视口底部30%处时触发
          end: "center center",
          scrub: 1,
          markers: false,
        },
      });
  
      // 添加渐入和上移动画
      sectionTl.from(section, {
        y: 80,
        opacity: 0.3,
        duration: 0.5,
        ease: "power2.out",
      });
    });
  
    // 为 asiainfo-book 添加动画
    const asiainfoBookLeft = document.querySelector(".asiainfo-book-left");
    const asiainfoBookRight = document.querySelector(".asiainfo-book-right");
  
    if (asiainfoBookLeft && asiainfoBookRight) {
      // 设置初始状态
      gsap.set([asiainfoBookLeft, asiainfoBookRight], {
        transformOrigin: "center center",
        transformPerspective: 1000,
      });
  
      // 创建时间轴
      const bookTl = gsap.timeline({
        scrollTrigger: {
          trigger: ".asiainfo-book",
          start: "top center",
          end: "bottom center",
          // scrub: 1,
        },
      });
  
      // 添加书页展开动画
      bookTl
        .from(asiainfoBookLeft, {
          x: -100,
          opacity: 0,
          duration: 0.8,
          ease: "power2.out",
        })
        .from(
          asiainfoBookRight,
          {
            x: 100,
            opacity: 0,
            duration: 0.8,
            ease: "power2.out",
          },
          "-=0.8"
        ); // 在左页动画完成前0.8秒开始
    }
  
    // 为 kwah-award 添加旋转动画
    const kwahAward = document.querySelector(".kwah-award");
    if (kwahAward) {
      // 首先添加 3D 透视效果
      gsap.set(kwahAward, {
        transformPerspective: 1000,
        transformStyle: "preserve-3d",
        transformOrigin: "center center",
      });
  
      // 创建旋转动画函数
      function rotateAward() {
        gsap.to(kwahAward, {
          rotationY: 360,
          duration: 2,
          ease: "power1.inOut",
          onUpdate: function () {
            // 添加光泽效果
            const progress = this.progress();
            const brightness = 1 + Math.sin(progress * Math.PI) * 0.2;
            gsap.set(kwahAward, {
              filter: `brightness(${brightness})`,
            });
          },
          onComplete: function () {
            // 重置旋转
            gsap.set(kwahAward, { rotationY: 0 });
            // 等待5秒后再次旋转
            setTimeout(rotateAward, 5000);
          },
        });
      }
      // 开始第一次旋转
      rotateAward();
    }
  
    // Slot Machine Animation
    const slotMachine = document.querySelectorAll(".slot-machine .holder");
    if (slotMachine.length > 0) {
      slotMachine.forEach((holder) => {
        // 创建 ScrollTrigger
        ScrollTrigger.create({
          trigger: holder,
          start: "top 80%",
          end: "bottom center",
          markers: false,
          onEnter: () => {
            // 获取 holder id
            const holderId = holder.getAttribute('data-id');
            if (holderId) {
              // 计算背景位置
              const position = (parseInt(holderId)) * 89.2; // 每个数字高度90px
              
              // 添加动画
              gsap.to(holder, {
                backgroundPosition: `0 ${-position}px`,
                duration: 1,
                ease: "power2.inOut",
                overwrite: true
              });
            }
          }
        });
      });
    }
  
    const  LightStick = document.querySelectorAll('.light-stick');
    if (LightStick.length > 0) {
      LightStick.forEach((stick, index) => {
        const scanTl = gsap.timeline({
          repeat: -1,
          repeatDelay: 0
        });
        const getRandomDuration = () => 1 + Math.random() * 1.5; // 1-2.5秒之间
        const getRandomDelay = () => 1 + Math.random() * 1.5; // 1-2.5秒之间
        // 添加背景位置动画
        scanTl
          .fromTo(stick, {
          }, {
            duration: getRandomDuration(),
            delay: getRandomDelay(),
            ease: "power1.inOut",
            onUpdate: function() {
              const progress = this.progress();
              // 动态调整背景混合模式和亮度
              const brightness = 1 + Math.sin(progress * Math.PI) * 0.4;
              stick.style.filter = `brightness(${brightness})`;
              stick.style.mixBlendMode = progress > 0.5 ? 'normal' : 'screen';
            }
          })
          .to(stick, {
            duration: 2,
            ease: "power1.inOut"
          });
        // 添加随机延迟开始
      });
    }
    
    const BookBgPattern = document.querySelectorAll('.book-bg-pattern');
    if (BookBgPattern.length > 0) {
      BookBgPattern.forEach((pattern, index) => {
        // 创建扫光动画时间轴
        const scanTl = gsap.timeline({
          repeat: -1,
          repeatDelay: 0
        });
        // 添加背景位置动画
        scanTl
          .fromTo(pattern, {
          }, {
            duration: 3,
            ease: "power1.inOut",
            onUpdate: function() {
              const progress = this.progress();
              // 动态调整背景混合模式和亮度
              const brightness = 1 + Math.sin(progress * Math.PI) * 0.8;
              pattern.style.filter = `brightness(${brightness})`;
              pattern.style.mixBlendMode = progress > 0.5 ? 'normal' : 'screen';
            }
          })
          .to(pattern, {
            duration: 1.5,
            ease: "power1.inOut"
          });
        // 添加随机延迟开始
        scanTl.delay(index * 0.5);
  
      });
    }
  
    // 为 book-cube 添加浮动动画
    const bookCubes = document.querySelectorAll('.book-cube');
    if (bookCubes.length > 0) {
      bookCubes.forEach((cube, index) => {
        // every cube has a random duration
        const randomDuration = 2 + Math.random() * 1.5;
        const randomY = 10 + Math.random() * 10;
        const randomDelay = Math.random() * 0.5;
  
        // 创建浮动动画
        gsap.to(cube, {
          y: randomY,
          duration: randomDuration,
          delay: randomDelay,
          yoyo: true,
          repeat: -1,
          ease: "sine.inOut",
        });
      });
  
    }
    const bgPatterns = document.querySelectorAll('.bg-pattern');
      if (bgPatterns.length > 0) {
        bgPatterns.forEach((pattern, index) => {
          // 设置初始状态
          gsap.set(pattern, {
            opacity: 1
          });
        
          // 生成随机持续时间
          const getRandomDuration = () => 1 + Math.random() * 1.5; // 1-2.5秒之间
        
          // 创建动画时间轴
          const patternTl = gsap.timeline({
            scrollTrigger: {
              trigger: pattern,
              start: "top bottom",
              end: "bottom top",
              toggleActions: "play none none reverse"
            },
            repeat: -1
          });
        
          // 添加渐变动画
          patternTl
            .to(pattern, {
              opacity: 1,
              duration: getRandomDuration(),
              ease: "power2.inOut"
            })
            .to(pattern, {
              opacity: 0.2,
              duration: getRandomDuration(),
              ease: "power2.inOut"
            })
            .to(pattern, {
              opacity: 1,
              duration: getRandomDuration(),
              ease: "power2.inOut"
            });
          
          // 添加延迟，使每个图案的动画不同步
          patternTl.delay(index * 0.5);
        });
      } 
      // 为 book-layer 添加旋转动画
  const bookLayers = document.querySelectorAll('.book-layer');
    if (bookLayers.length > 0) {
      bookLayers.forEach((layer, index) => {
        // 设置初始状态
        gsap.set(layer, {
          transformOrigin: "center center"
        });
  
        const getRandomDuration = () => 10 + Math.random() * 1.5; // 1-2.5秒之
        const getRandomDelay = () => 1 + Math.random() * 1.5; // 1-2.5秒之间
  
  
        // 创建动画
        gsap.to(layer, {
          rotation: "360", // 随机旋转角度
          scale: "1", // 随机缩放
          duration: getRandomDuration(), // 随机持续时间
          repeat: -1,
          yoyo: false,
          ease: "sine.inOut",
          delay: getRandomDelay(), // 错开每个图层的动画开始时间
          onComplete: function() {
            // 动画完成后随改变下一次的旋转角度
            
          }
        });
      });
    }
  // });
}
