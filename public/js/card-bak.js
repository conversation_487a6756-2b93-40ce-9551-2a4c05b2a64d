let root;
let cardGroup;
let mouseX = window.innerWidth / 2;
let mouseY = window.innerHeight / 2;
let onMouseX;
let onMouseY;
let ox;
let oy;
let devicePixel = window.devicePixelRatio;
let n = 20;
let m = 24;
let w = 500/3;
let h = 680/3;
let cw = (w+m)*n;
let ch = (h+m)*10;
let array = [0xee7777, 0x77eedf, 0xb2ee77, 0xe477ee, 0xedee77, 0x7779ee, 0xee7777, 0x77eedf, 0xb2ee77, 0xe477ee];
let textureArray = ['json/001_e_6999_fullset.jpeg', 'json/002_e_3913_fullset.jpeg', 'json/003_e_1597_fullset.jpeg', 'json/004_e_1971_fullset.jpeg', 'json/005_e_6918_fullset.jpeg', 'json/006_e_2381_fullset.jpeg','json/e_02127_cover.jpeg', 'json/e_09982_cover.jpeg'];
//const ar = "http://www.cre8corp.com/eng/ar.json?v=20201130&_";
//const ipo = "http://www.cre8corp.com/eng/ipo.json?v=20201130&_";
//const oc = "http://www.cre8corp.com/eng/oc.json?v=20201130&_";
const ar = "json/ar.json";
const ipo = "json/ipo.json";
const oc = "json/oc.json";
let arList;
let ipoList;
let ocList;
let allList = [];
let loaded = 0;
let totalCount = 0;
let isDrag = false;

$(document).ready(function(){

    //credentialInit();

});

function credentialInit(){
    root = new THREERoot({
      container: '#credential-container',
      fov: 45,
      aspect: $(window).width()/$(window).height(),
      near: 0.1,
      far: 5000,
      alpha: true,
      createCameraControls: false
    });
    root.renderer.setClearColor(0x000000, 0);
    //root.renderer.shadowMap.enabled = true;
    //root.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    root.camera.position.set(-cw*.15, -ch*.15, 800);
    root.camera.rotation.x = Math.PI*.15;
    root.camera.rotation.y = -Math.PI*.15;
    root.camera.rotation.z = -Math.PI*.15;

    //let light = new THREE.DirectionalLight(0xffffff, 1, 100);
    let light = new THREE.PointLight(0xffffff, 1, 8000);
    light.position.set(0,0, 200);
    light.castShadow = true;
    light.shadow.mapSize.width = 2048;
    light.shadow.mapSize.height = 2048;
    root.scene.add(light);

    /*const helper = new THREE.CameraHelper( light.shadow.camera );
    root.scene.add( helper );*/

    let interaction = new THREE.Interaction(root.renderer, root.scene, root.camera);

    cardGroup = new THREE.Group();
    cardGroup.name = "cardGroup";
    root.scene.add(cardGroup);

    let bgGeo = new THREE.PlaneBufferGeometry(cw, ch, 1, 1);
    let bgMaterial = new THREE.MeshBasicMaterial({color: 0xFFFFFF, transparent: true, wireframe : false});
    let bg = new THREE.Mesh(bgGeo, bgMaterial);
    //bg.receiveShadow = true;
    //bg.position.z = -50;
    //cardGroup.add(bg);
    //root.scene.add(bg);

    let objects = [cardGroup];

    insertList(ar, "arList");
    insertList(ipo, "ipoList");
    insertList(oc, "ocList");

    /*for(let i=0; i<n; i++){
        for(let j=0; j<n; j++){
            let book = new THREE.Group();
            //let cardGeo = new THREE.PlaneBufferGeometry(w, h, 1, 1);
            let cardGeo = createBoxWithRoundedEdges(w, h, 10, 10, 1);
            let shadowMaterial = new THREE.MeshBasicMaterial({color: 0xAAAAAA, transparent: true, opacity : .5});
            let cardMaterial = new THREE.MeshBasicMaterial({color: array[i], transparent: true, opacity : 1});
            let card = new THREE.Mesh(cardGeo, cardMaterial);
            let shadow = new THREE.Mesh(cardGeo, shadowMaterial);
            card.position.z = 10;
            card.name = "card";
            shadow.name = "shadow";

            book.add(card);
            book.add(shadow);

            book.position.x = i*(w+m)-cw/2+w/2+m/2;
            book.position.y = j*(h+m)-ch/2+h/2+m/2;

            //book.position.x = i*(w+m);
            //book.position.y = j*(h+m);

            book.name = i;
            cardGroup.add(book);
        }
    }*/

    window.addEventListener('mousedown', onDown, false);
    window.addEventListener('mousemove', onMove, false);

    //const controls = new THREE.DragControls( objects, root.camera, root.renderer.domElement );
}

function onCredentialMove(e){

}

function onDown(e){
    onMouseX = e.offsetX;
    onMouseY = e.offsetY;
    ox = cardGroup.position.x;
    oy = cardGroup.position.y;
    window.addEventListener('mousemove', onDrag, false);
    window.addEventListener('mouseup', onUp, false);
}

function onUp(e){
    window.removeEventListener('mousemove', onDrag, false);
    window.removeEventListener('mouseup', onUp, false);
    isDrag = false;
}

function onMove(e){
    let x = e.offsetX;
    let y = e.offsetY;
    let dx = x-$(window).width()/2;
    let dy = y-$(window).height()/2;

    //cardGroup.rotation.z = dx/10000;
    //root.camera.rotation.z = dy/10000;
    //root.camera.rotation.x = Math.PI*(dy/8000);
}

function onDrag(e){
    isDrag = true;
    let x = e.offsetX;
    let y = e.offsetY;

    let dx = onMouseX-x;
    let dy = onMouseY-y;

    //cardGroup.position.x = ox-dx;
    //cardGroup.position.y = oy+dy;
    gsap.to(cardGroup.position, .5, {x:ox-dx*1.5, y:oy+dy*1.5});
    //gsap.to(cardGroup.position, .5, {x:ox-dx*2});

    $(cardGroup.children).each(function(){
        let cdx = ox-dx*2+this.position.x;
        let cdy = oy-dy*2+this.position.y;
        //let cdx = cardGroup.position.x+this.position.x;
        let lx = cw*.7-(w+m)/2;
        let ly = ch*.7-(h+m)/2;
        if(cdx < -lx){
            this.position.x += cw;
            let c = this.getObjectByName( "card" );
            c.position.z = 100;
            c.rotation.y = Math.PI;
            gsap.to(c.rotation, .5, {y:0});
            gsap.to(c.position, .5, {z:10});
        }else if(cdx > lx){
            this.position.x -= cw;
            let c = this.getObjectByName( "card" );
            c.position.z = 100;
            c.rotation.y = Math.PI;
            gsap.to(c.rotation, .5, {y:0});
            gsap.to(c.position, .5, {z:10});
        }

        if(cdy < -ly){
            this.position.y += ch;
            let c = this.getObjectByName( "card" );
            c.position.z = 100;
            c.rotation.y = Math.PI;
            gsap.to(c.rotation, .5, {y:0});
            gsap.to(c.position, .5, {z:10});
        }else if(cdy > ly){
            this.position.y -= ch;
            let c = this.getObjectByName( "card" );
            c.position.z = 100;
            c.rotation.y = Math.PI;
            gsap.to(c.rotation, .5, {y:0});
            gsap.to(c.position, .5, {z:10});
        }
    });
}

function createBoxWithRoundedEdges( width, height, depth, radius0, smoothness ) {
  /*let shape = new THREE.Shape();
  let eps = 0.00001;
  let radius = radius0 - eps;
  shape.absarc( eps, eps, eps, -Math.PI / 2, -Math.PI, true );
  shape.absarc( eps, height -  radius * 2, eps, Math.PI, Math.PI / 2, true );
  shape.absarc( width - radius * 2, height -  radius * 2, eps, Math.PI / 2, 0, true );
  shape.absarc( width - radius * 2, eps, eps, 0, -Math.PI / 2, true );
  let geometry = new THREE.ExtrudeBufferGeometry( shape, {
    amount: depth - radius0 * 2,
    bevelEnabled: true,
    bevelSegments: smoothness * 2,
    steps: 1,
    bevelSize: radius,
    bevelThickness: radius0,
    curveSegments: smoothness
  });*/
  //let geometry = new THREE.BoxBufferGeometry(width, height, depth, 1, 1, 1);
  let geometry = new THREE.PlaneBufferGeometry(width, height, 1, 1);
  //let material = 
  
  geometry.center();
  
  return geometry;
}

function insertList(jsonList, store){
  $.ajax({
    type: "GET",
    url: jsonList,
    dataType: "json",
    //cache: false,
    success: function(data) {
      allList.push(data);

      count = data.length;

      for(var i=0;i<count;i++){
        allList.push(data[i]);
      }
      //console.log(count);
      totalCount += count;
      loaded++;
      if(loaded == 3){
        console.log('complete');
        let n = Math.ceil(totalCount/10);
        cw = (w+m)*n;
        //console.log(allList);
        let t = 0;
        let k = 0;

        for(let i=0; i<n; i++){
            for(let j=0; j<10; j++){
                //console.log(allList.length);
                let book = new THREE.Group();
                //let cardGeo = new THREE.PlaneBufferGeometry(w, h, 1, 1);
                //let texture = new THREE.TextureLoader().load(allList[t].img);
                if(t > textureArray.length-1){
                    t = 0;
                }
                
                let texture = new THREE.TextureLoader().load(textureArray[t]);
                //console.log(allList[k].img);
                //let texture = new THREE.TextureLoader();
                //texture.crossOrigin = '';
                //let mapOverlay = texture.load(allList[k].img);
                let cardGeo = createBoxWithRoundedEdges(w, h, 2, 10, 1);
                let shadowMaterial = new THREE.MeshBasicMaterial({color: 0xAAAAAA, transparent: true, opacity : .5});
                let cardMaterial = new THREE.MeshBasicMaterial({map: texture, transparent: true, opacity : 1});
                let card = new THREE.Mesh(cardGeo, cardMaterial);
                let shadow = new THREE.Mesh(cardGeo, shadowMaterial);
                card.position.z = 10;
                card.name = "card";
                shadow.name = "shadow";

                book.add(card);
                book.add(shadow);
                book.cursor = 'pointer';

                book.position.x = i*(w+m)-cw/2+w/2+m/2;
                book.position.y = j*(h+m)-ch/2+h/2+m/2;
                //book.info = {code: allList[k].code, name: allList[k].name, img: allList[k].img, pdf: allList[k].pdf};
                book.info = allList[k];

                //book.position.x = i*(w+m);
                //book.position.y = j*(h+m);

                book.on('mouseover',function(ev){
                    if(!isDrag){
                        let c = this.getObjectByName('card');
                        gsap.to(c.position, .5, {z: 100});
                        this.userData = {isOver: true};
                    }
                });
                book.on('mouseout',function(ev){
                    if(!isDrag){
                        let c = this.getObjectByName('card');
                        gsap.to(c.position, .5, {z: 10});
                        this.userData = {isOver: false};
                    }
                });
                book.on('mouseup', function(ev) {
                  if(this.userData.isOver){
                    window.removeEventListener('mousedown', onDown, false);
                    window.removeEventListener('mousemove', onMove, false);
                    $('.pop-up').addClass('show');
                    $('.pop-up__content').empty();
                    $('.pop-up__content').append('<img src="'+this.info.img+'" />');
                    $('.pop-up__content').append('<p>'+this.info.name+'</p>');
                    $('.pop-up__content').append('<p class="close-btn"></p>');
                    $('.pop-up__content .close-btn').on('click',function(){
                        $('.pop-up').removeClass('show');
                        window.addEventListener('mousedown', onDown, false);
                        window.addEventListener('mousemove', onMove, false);
                    })
                    if(this.info.pdf){
                        $('.pop-up__content').append('<a href="'+this.info.pdf+'" target="_blank">View</a>');
                        $('.pop-up__content').append('<a href="'+this.info.pdf+'" download target="_blank">Download</a>');
                    }
                  }
                });

                book.name = i;
                cardGroup.add(book);

                t++;
                k++;
            }
        }
      }
    }
  });
}