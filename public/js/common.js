var windowWidth;
var windowHeight;
let homeTxtNum = 0;
let indexNum = 0;
let homeTxt = ['Innovate', 'Understand', 'Echo', 'Care', 'Believe'];
let homeTitleTimeout;


$(document).ready(function(){
	//gsap.registerPlugin(ScrollTrigger);

	$('.menu-btn').bind('click', onMenuShow);
	$('.close-btn').bind('click', onMenuShow);
	$('.bg-mode').bind('click', brightMode);

	$('body').append('<audio id="audio" loop=""><source src="/images/Reflection.mp3"></audio>');

	$('.menu-holder .bg-mode').after('<div class="sound-btn"></div>');

	$('.sound-btn').bind('click',soundOnOff);

	$('.sns-in').click(function(e){
		e.preventDefault();
		window.open('https://hk.linkedin.com/company/cre8-greater-china-ltd');
	});

	$('.sns-fb').click(function(e){
		e.preventDefault();
		window.open('https://www.facebook.com/cre8ir');
	});

	$('.sns-wc').click(function(e){
		e.preventDefault();
		// window.open('https://www.facebook.com/cre8ir');
		$('.popup').show(0);
		$('.popup').click(function(e){
			$('.popup').hide(0);
		});
	});

	$('body').append('<div class="popup"><img src="/images/getqrcode.jpg" alt=""></div>');



	var date = new Date();
	var hr = date.getHours();
	if(hr >= 6 && hr < 18){
		$('#bg').removeClass('night').addClass('day');
		$('.bg-mode').removeClass('dark');
	}else{
		$('#bg').removeClass('day').addClass('night');
		$('.bg-mode').addClass('dark');
	}

	init();

	onResize();
	$(window).bind('scroll',onWindowScroll);
	$(window).bind('resize',onResize);

	$('.lang a').bind('click', changeLang);


	window.onpopstate = history.onpushstate = function(e) {
		let url = window.location.href;
      	loadPage(url);
    };
});

function init(){
	appendScript("https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js");
	appendScript("https://cdn.tailwindcss.com");
	indexNum = 0;
	windowWidth = $(window).width();
	windowHeight = $(window).height();
	onWindowScroll();


    $(window).unbind('mousemove', onCredentialMove);
    $(window).unbind('wheel', onCredentialScroll);

	if($('#bg').hasClass('night')){
		$('body').addClass('home');
	}

	setTimeout(function(){
		$('#bg').addClass('ani-enlarge');
	}, 500);

	$('.content').addClass('transit-in');

	if($('.index-holder').length > 0){
		$('.index-holder').each(function(){
			$('.index-menu a', this).bind('mouseenter', onIndexOver);
			let n = $('.index-menu a.active', this).index();
			gsap.to($(this), 1, {rotate: n*45});
		});

		$('.index').bind('mouseleave', onIndexLeave);
	}

	if($('.home-title').length > 0){
		setTimeout(function(){
			$('.home-title').addClass('active');
			$('.home-title__first p').each(function(i){
				gsap.to($('span',this), .5, {x: 0, delay: .1*i});
			});
			$('.slide-circle').each(function(){
				gsap.to($(this), .8, {scale: 2, top: Math.random()*windowHeight, left: Math.random()*windowWidth, ease: Power2.easeInOut});
				//$(this).stop().animate({top: Math.random()*windowHeight, left: Math.random()*windowWidth}, 800, 'easeInOutQuart');
			});
			//homeTitleText();
			var messenger = new Messenger($('#messenger'));
		}, 5000);
	}

	if($('.facilities-slider').length > 0){
		var swiper = new Swiper('.facilities-slider', {
			speed: 800,
	        slidesPerView: "auto",
	        loop: true,
	        centeredSlides: true,
	        spaceBetween: 30,
	        autoplay: {
	          delay: 3000,
	          disableOnInteraction: false,
	        },
	        pagination: {
	          el: ".swiper-pagination",
	          clickable: true,
	        },
	      });
	}

	$('.slide-title').mouseenter(function(){
		$(this).parent().find('.slide-circle-holder').addClass('hover');
	});

	$('.slide-title').mouseleave(function(){
		$(this).parent().find('.slide-circle-holder').removeClass('hover');
	});

	// $('.page-change').unbind('click',pageChange);
	// $('.page-change').bind('click',pageChange);

	$('.anchor-change').unbind('click',anchorChange);
	$('.anchor-change').bind('click',anchorChange);

	if($('#credential-container').length > 0 ){
		credentialInit();
	}

	if($('#home-video').length > 0){
		$('#home-video')[0].play();
	}

}

function soundOnOff(e){
	if($(this).hasClass('sound-on')){
		$('.sound-btn').removeClass('sound-on')
		$('#audio')[0].pause();
	}else{
		$('.sound-btn').addClass('sound-on')
		$('#audio')[0].play();
	}
}

function changeLang(e){
	// e.preventDefault();
	// let n = $(this).index();
	// let url = window.location.href;
	// if(n == 0 && url.indexOf('/tc/') > 0){
	// 	url = url.replace('/tc/', '/en/')
	// }else if(n == 0 && url.indexOf('/sc/') > 0){
	// 	url = url.replace('/sc/', '/en/')
	// }else if(n == 1 && url.indexOf('/en/') > 0){
	// 	url = url.replace('/en/', '/tc/')
	// }else if(n == 1 && url.indexOf('/sc/') > 0){
	// 	url = url.replace('/sc/', '/tc/')
	// }else if(n == 2 && url.indexOf('/en/') > 0){
	// 	url = url.replace('/en/', '/sc/')
	// }else if(n == 2 && url.indexOf('/tc/') > 0){
	// 	url = url.replace('/tc/', '/sc/')
	// }
	// window.location.href = url;
}

function homeTitleText(){
	//home-title__second
	let txt = homeTxt[homeTxtNum];
	for(var i=0;i< txt.length;i++){
		$('.home-title__second').append('<p><span>'+txt[i]+'</span></p>');
		gsap.to($('.home-title__second span'), .5, {x: 0, delay: .1*i});
	}
	setTimeout(changeHomeTxt, 6000);

}

function changeHomeTxt(){
	homeTxtNum++;
	if(homeTxtNum > homeTxt.length-1){
		homeTxtNum = 0;
	}
	let txt = homeTxt[homeTxtNum];
	$('.home-title__second').empty();
	for(var i=0;i< txt.length;i++){
		$('.home-title__second').append('<p><span>'+txt[i]+'</span></p>');
		gsap.to($('.home-title__second span'), .5, {x: 0, delay: .1*i});
	}
	homeTitleTimeout = setTimeout(changeHomeTxt, 6000);
}

function onIndexOver(e){
	let parent = $(this).parent();
	let grandParent = $(parent).parent();
	let n = $('a', parent).index(this);
	if(n == indexNum){
		return false;
	}
	indexNum = n;
	$('.index-menu a').unbind('mouseenter', onIndexOver);
	$(grandParent).stop().transition({rotate: 45*n+'deg'}, 1000, function(){
		$('.index-menu a').bind('mouseenter', onIndexOver);
		setTimeout(function(){
			let n = $('.index-menu a:hover').index();
			if(n != -1){
				$('.index-menu a:hover').mouseenter();
			}
		}, 300);
		//console.log($('.index-menu a:hover').index());
	});
}

function onIndexLeave(e){
	let n = $('.index-menu a.active', this).index();
	$('.index-holder', this).stop().transition({rotate: 45*n+'deg'}, 1000);
}

function pageChange(e){
	e.preventDefault();
	$('body').removeClass('home');
	$('.content').removeClass('transit-in');
	var path = $(this).attr('href');
	history.pushState({},'', path);

	$('#bg').removeClass('transit-finish');
	setTimeout(function(){
		$('#bg').addClass('ani-shink');
	}, 100);
	setTimeout(function(){
		$('#bg').removeClass('ani-shink');
		loadPage(path);
	}, 1500);

	if($('.menu').hasClass('active')){
		$('.close-btn').click();
	}
}

function anchorChange(e){
	e.preventDefault();
	let n = $(this).index();
	let y = $('.about-container__section').eq(n).position().top;
	$('html, body').stop().animate({scrollTop: y, function(){
		onWindowScroll();
	}});
}
function appendScript(src){
	const script = document.createElement('script');
	document.head.appendChild(script)
	script.src = src;

}

function changeHeadTitle(url){
	$.ajax({
		type: "GET",
		url: "/headTitle.json",
		dataType: "json",
		cache: false,
	}).then(function(data){
		console.log(data,'data');
		data.forEach(item => {
			if(item.url == url ) {
			  document.title = item.head_title;
			  return;
			}
		});
		return;
	});
}

function loadPage(url){
	console.log('loadPage...', url);
	clearTimeout(homeTitleTimeout);
	$.ajax({
		xhr: function() {
			var xhr = new window.XMLHttpRequest();
			xhr.addEventListener("progress", function(evt) {
				if (evt.lengthComputable) {
					var percentComplete = evt.loaded / evt.total;
					percentComplete *= 100;
					//$('.loading p').css({width:percentComplete+'%'});
				}
			}, false);

			return xhr;
		},
		type: 'GET',
		cache: false,
		url: url + "?t=" + new Date().getTime(),
		success: function(data){
			var content = $(data).find('.content');
			$('.content').eq(0).addClass('transit-out');
			/*$('img, source', content).each(function(){
				var src = $(this).attr('src');
				src = src.split('../')[1];
				$(this).attr('src',src);
			});*/
			//$('#wrapper').append(content);
			setTimeout(function(){
				$('.content').eq(0).remove();
				$('#wrapper').append(content);
				init();
				// append award_2024.js
				changeHeadTitle(url);
				if(url.includes('awards')){
					appendScript("/js/award_2024.js");
					// appendScript("https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js");
					// appendScript("https://cdn.tailwindcss.com");
					setTimeout(function(){
						document.querySelector('video').play();
					}, 1000);
				}

				if(url.includes('ir')){
					loadIRPage();
				}

				$.getScript( "//webapi.amap.com/maps?v=1.4.15&key=b5d9b672e4ce4efdf6e98fd901245da4", function( data, textStatus, jqxhr ) {
					console.log("init");
					// const langs = {
					// 	'en': 'en',
					// 	'tc': 'zh_en',
					// 	'sc': 'zh_cn'
					// }
					let lang = '';
					if (location.href.includes('/tc/')) {
						lang = 'zh_en';
					}else if (location.href.includes('/sc/')) {
						lang = 'zh_cn';
					}else if (location.href.includes('/en/')) {
						lang = 'en';
					}

					if (document.getElementById('amap')) {
					  var map = new AMap.Map('amap', {
						  lang: lang,
						  resizeEnable: true,
						  center: [114.161884,22.279039],
							  zoom:20
					  });
					  var marker = new AMap.Marker({
						  position: new AMap.LngLat(114.161884,22.279039),
						  offset: new AMap.Pixel(-40, 5),
						  // icon: '//vdata.amap.com/icons/b18/1/2.png', // 添加 Icon 图标 URL
						  icon: '//www.cre8corp.com/images/logo-b.png', // 添加 Icon 图标 URL
						  title: 'China Building'
					  });
					  var marker2 = new AMap.Marker({
						  position: new AMap.LngLat(121.456246,31.231066),
						  offset: new AMap.Pixel(-40, 5),
						  // icon: '//vdata.amap.com/icons/b18/1/2.png', // 添加 Icon 图标 URL
						  icon: '//www.cre8corp.com/images/logo-b.png', // 添加 Icon 图标 URL
						  title: '嘉地中心'
					  });

						map.add(marker);
						map.add(marker2);
					}
				});
				//history.pushState(null, null, path);
			},1000);
		}
	});
}

function brightMode(){
	if($(this).hasClass('dark')){
		$('#bg').removeClass('night').addClass('day');
		$('.bg-mode').removeClass('dark');

		$('body').removeClass('home');
	}else{
		$('#bg').removeClass('day').addClass('night');
		$('.bg-mode').addClass('dark');

		$('body').addClass('home');
	}
}


function onHashChange(){
}

function onMenuShow(e){
	e.preventDefault();
	if($('.menu').hasClass('active')){
		$('.menu-mask, .menu').removeClass('active');
		$(this).removeClass('active');
	}else{
		$('.menu-mask, .menu').addClass('active');
		$(this).addClass('active');
	}
}

function backHistory(e){
	e.preventDefault();
	history.back();
}

/************************** onScroll **************************/
function onMouseMove(e){
  var _x = e.clientX-windowWidth/2;
  var _y = e.clientY-windowHeight/2;
}
function onMousewheel(e){
}

function onTouchstart(e){
}

function onTouchmove(e){
}

function onTouchend(e){
}

function onWindowScroll(e){
	let st = $(window).scrollTop();
	if($('.about-container__section').length > 0){
		$('.about-container__section').each(function(){
			//console.log($(this).position().top, st);
			if($(this).position().top < st+windowHeight/2 && !$(this).hasClass('show')){
				let n = $(this).index();
				$(this).addClass('show');
				$('.layer',this).addClass('visible');
				$('.index--about a').removeClass('active');
				$('.index--about a').eq(n).addClass('active');
				//$('.index--about .index-holder').stop().transition({rotate: 45*n+'deg'}, 2000);
				//$('.index-menu a').unbind('mouseenter', onIndexOver);
				gsap.to($('.index--about .index-holder'), 1, {rotate: n*45});
			}else if($(this).position().top >= st-windowHeight/2 && $(this).hasClass('show')){
				$(this).removeClass('show');
			}
		});
	}
}
/************************** onScroll **************************/
var Messenger = function(el){
  'use strict';
  var m = this;

  m.init = function(){
    m.codeletters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ|/";
    m.message = 0;
    m.current_length = 0;
    m.fadeBuffer = false;
    m.messages = [
      'Innovate',
      'Understand',
      'Echo',
      'Care',
      'Believe'
    ];

    setTimeout(m.animateIn, 100);
  };

  m.generateRandomString = function(length){
    var random_text = '';
    while(random_text.length < length){
      random_text += m.codeletters.charAt(Math.floor(Math.random()*m.codeletters.length));
    }

    return random_text;
  };

  m.animateIn = function(){
    if(m.current_length < m.messages[m.message].length){
      m.current_length = m.current_length + 2;
      if(m.current_length > m.messages[m.message].length) {
        m.current_length = m.messages[m.message].length;
      }

      var message = m.generateRandomString(m.current_length);
      $(el).html(message);

      setTimeout(m.animateIn, 20);
    } else {
      setTimeout(m.animateFadeBuffer, 20);
    }
  };

  m.animateFadeBuffer = function(){
    if(m.fadeBuffer === false){
      m.fadeBuffer = [];
      for(var i = 0; i < m.messages[m.message].length; i++){
        m.fadeBuffer.push({c: (Math.floor(Math.random()*12))+1, l: m.messages[m.message].charAt(i)});
      }
    }

    var do_cycles = false;
    var message = '';

    for(var i = 0; i < m.fadeBuffer.length; i++){
      var fader = m.fadeBuffer[i];
      if(fader.c > 0){
        do_cycles = true;
        fader.c--;
        message += m.codeletters.charAt(Math.floor(Math.random()*m.codeletters.length));
      } else {
        message += fader.l;
      }
    }

    $(el).html(message);

    $(el).addClass('active');

    if(do_cycles === true){
      setTimeout(m.animateFadeBuffer, 50);
    } else {
      setTimeout(m.cycleText, 5000);
    }
  };

  m.cycleText = function(){
    m.message = m.message + 1;
    if(m.message >= m.messages.length){
      m.message = 0;
    }

    m.current_length = 0;
    m.fadeBuffer = false;
    $(el).removeClass('active');
    $(el).html(m.messages[m.message-1]);

    setTimeout(m.animateIn, 200);
  };




  m.init();
}

//console.clear();
/************************** onResize **************************/
function onResize(){
	windowWidth = $(window).width();
	windowHeight = $(window).height();
	if(windowWidth <= 768){
		$('body').addClass('mobile');

    	$(window).unbind('mousemove', onCredentialMove);
    	$(window).unbind('wheel', onCredentialScroll);

    	$('.camera, .credential-list').removeAttr('style');
	}else{
		$('body').removeClass('mobile');

		if($('#credential-container').length > 0 ){
    		$(window).bind('mousemove', onCredentialMove);
    		$(window).bind('wheel', onCredentialScroll);
    	}
	}
}
/************************** onResize **************************/

jQuery.event.add(window,"load",function() {

});

function addCommas(nStr) {
    nStr += '';
    x = nStr.split('.');
    x1 = x[0];
    x2 = x.length > 1 ? '.' + x[1] : '';
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
        x1 = x1.replace(rgx, '$1' + ',' + '$2');
    }
    return x1 + x2;
}

var delay = (function(){
	var timer = 0;
	return function(callback, ms){
		clearTimeout(timer);
		timer = setTimeout(callback, ms);
	};
})();

setTimeout(()=>{
	loadIRPage();
}, 1000);

function loadIRPage() {

	// const is_ir_page = document.querySelector("#irPage")
	// console.log('load ir page', is_ir_page );
	// if (!is_ir_page) return;

	const list_docs = [
		{
			"des": "8-A12B (Registration statement)",
			"form": "",
			"date": "2025-07-22",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025066459/ea0249823-8a12b_cre8.htm"
		},
		{
			"des": "CERT",
			"form": "",
			"date": "2025-07-22",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000135445725000722/8A_Cert_CRE.pdf"
		},
		{
			"des": "EFFECT (SEC order)",
			"form": "",
			"date": "2025-07-22",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/999999999525002308/xslEFFECTX01/primary_doc.xml"
		},
		{
			"des": "POS EX",
			"form": "",
			"date": "2025-07-21",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025066018/ea0201513-22.htm"
		},
		{
			"des": "POS AM (Post-effective amendment)",
			"form": "",
			"date": "2025-05-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025047137/ea0201513-21.htm"
		},
		{
			"des": "20-F (Annual report - foreign issuer)",
			"form": "",
			"date": "2025-05-19",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025045469/ea0241024-20f_cre8.htm"
		},
		{
			"des": "EFFECT (SEC order)",
			"form": "",
			"date": "2025-03-31",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/999999999525000871/xslEFFECTX01/primary_doc.xml"
		},
		{
			"des": "CORRESP (Correspondence)",
			"form": "",
			"date": "2025-03-27",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000101376225003247/filename1.htm"
		},
		{
			"des": "CORRESP (Correspondence)",
			"form": "",
			"date": "2025-03-27",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000101376225003249/filename1.htm"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2025-03-03",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025019184/ea0201513-19.htm"
		},
		{
			"des": "FWP (Prospectus)",
			"form": "",
			"date": "2025-02-12",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025012330/ea0230633-fwp_cre8enter.htm"
		},
		{
			"des": "CORRESP (Correspondence)",
			"form": "",
			"date": "2025-02-03",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025009211/filename1.htm"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2025-02-03",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025009207/ea0201513-17.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2025-01-22",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000025000662/filename1.pdf"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2025-01-21",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390025004905/ea0201513-16.htm"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2024-12-17",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024109558/ea0201513-15.htm"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2024-11-18",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024099385/ea0201513-14.htm"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2024-10-24",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024090418/ea0201513-13.htm"
		},
		{
			"des": "CORRESP (Correspondence)",
			"form": "",
			"date": "2024-09-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024080946/filename1.htm"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2024-09-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024080944/ea0201513-12.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2024-09-10",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000024010202/filename1.pdf"
		},
		{
			"des": "F-1/A (Registration statement)",
			"form": "",
			"date": "2024-09-05",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024076037/ea0201513-09.htm"
		},
		{
			"des": "CORRESP (Correspondence)",
			"form": "",
			"date": "2024-09-05",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024076041/filename1.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2024-08-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000024009664/filename1.pdf"
		},
		{
			"des": "F-1 (Registration statement)",
			"form": "",
			"date": "2024-08-19",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024070408/ea0201513-08.htm"
		},
		{
			"des": "DRSLTR",
			"form": "",
			"date": "2024-07-29",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000101376224001886/filename1.htm"
		},
		{
			"des": "DRS/A (Draft registration statement)",
			"form": "",
			"date": "2024-07-29",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000101376224001884/filename1.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2024-07-26",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000024008488/filename1.pdf"
		},
		{
			"des": "DRS/A (Draft registration statement)",
			"form": "",
			"date": "2024-07-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024063375/filename1.htm"
		},
		{
			"des": "DRSLTR",
			"form": "",
			"date": "2024-07-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024063378/filename1.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2024-07-12",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000024007917/filename1.pdf"
		},
		{
			"des": "DRS/A (Draft registration statement)",
			"form": "",
			"date": "2024-06-21",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024054572/filename1.htm"
		},
		{
			"des": "DRSLTR",
			"form": "",
			"date": "2024-03-13",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024021969/filename1.htm"
		},
		{
			"des": "DRS/A (Draft registration statement)",
			"form": "",
			"date": "2024-03-13",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024021967/filename1.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2024-03-05",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000024002471/filename1.pdf"
		},
		{
			"des": "DRSLTR",
			"form": "",
			"date": "2024-02-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024016272/filename1.htm"
		},
		{
			"des": "DRS/A (Draft registration statement)",
			"form": "",
			"date": "2024-02-23",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390024016270/filename1.htm"
		},
		{
			"des": "UPLOAD (Correspondence) LETTER",
			"form": "",
			"date": "2024-01-18",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000000000024000668/filename1.pdf"
		},
		{
			"des": "DRS (Draft registration statement)",
			"form": "",
			"date": "2023-12-22",
			"href": "https://www.sec.gov/Archives/edgar/data/2003977/000121390023098049/filename1.htm"
		}
	]

	list_docs.forEach((item, index) => {
		item.download = {
			htm: item.href.split('.').pop() == 'htm' ? item.href : null,
			pdf: item.href.split('.').pop() == 'pdf' ? item.href : null,
			doc: item.href.split('.').pop() == 'doc' ? item.href : null,
			zip: item.href.split('.').pop() == 'zip' ? item.href : null,
			xml: item.href.split('.').pop() == 'xml' ? item.href : null,
		}
		item.form = /\((.+)\)/.test(item.des) ? item.des.replace(item.des.match(/\((.+)\)/)[0], "") : "";  // DRS
		item.des = /\((.+)\)/.test(item.des) ? item.des.match(/\((.+)\)/)[1] : item.des;  // Draft registration statement

		let download = ''
		if (item.download) {
			download += item.download.htm ? `<a class="htm" target="_blank" href="${item.download.htm}"><img src="../images/_htm.gif" alt=""></a>` : '';
			download += item.download.pdf ? `<a class="pdf" target="_blank" href="${item.download.pdf}"><img src="../images/_pdf.gif" alt=""></a>` : '';
			download += item.download.doc ? `<a class="doc" target="_blank" href="${item.download.doc}"><img src="../images/_doc.gif" alt=""></a>` : '';
			download += item.download.zip ? `<a class="zip" target="_blank" href="${item.download.zip}"><img src="../images/_zip.gif" alt=""></a>` : '';
			download += item.download.xml ? `<a class="xml" target="_blank" href="${item.download.xml}"><img src="../images/_xml.gif" alt=""></a>` : '';
		}
		bgClass = !(index % 2) ? 'bg-slate-400' : '';
		$('.json-table tbody').append(`
                            <tr class="${bgClass}">
                                <td class="p-2 ">${item.date}</td>
                                <td class="p-2 ">${item.form}</td>
                                <td class="p-2 ">${item.des}</td>
                                <td class="p-2 flex items-center gap-1">
                                    ${download}
                                </td>
                            </tr>`);
	});


	$('.ir-page .page').hide();
	$('.ir-page .page').eq(0).show();


	$('.submenu div').on('click', function () {
		$index = $('.submenu div').index(this);
		$('.submenu div').removeClass('active');
		$(this).addClass('active');
		$('.ir-page .page').hide();
		$('.ir-page .page').eq($index).show();

	});

	// fetch('/api/ir/cre/press-releases').then(res => res.json()).then(data => {
    //     const list = data.data.map(item => {
    //         return {
    //             date: item.created_at.split('T')[0],
    //             des: item.title,
    //             doc_id: item.doc_id,
    //             link: `/press-release/${item.doc_id}`,
    //         }
    //     }).map((item, index) => `<a class="flex items-center ${!(index % 2) ? 'bg-slate-400 hover:bg-slate-500' : 'hover:bg-slate-200'} py-4 px-8" target="_blank" href="${item.link}">
    //     <div class="flex w-full items-center justify-between gap-2">
    //         <span class="grow">${item.des}</span>
    //         <span class="text-sm text-slate-500">${item.date}</span>
    //     </div>
    //     </a>`).join('');
    //     $('#press-releases').html(list);
	// });

}

// 语言切换函数
function switchLanguage(lang) {
    // 获取当前页面路径
    const currentPath = window.location.pathname;
    
    if (lang === 'en' || lang === 'tc') {
        // 跳转到 cre8corp.com，保持当前页面路径
        let targetUrl = 'https://www.cre8corp.com/' + lang + currentPath;
        
        // 如果是首页，直接跳转到根目录
        if (currentPath === '/') {
            targetUrl = 'https://www.cre8corp.com/' + lang;
        }
        
        window.open(targetUrl, '_blank');
    } else if (lang === 'sc') {
        // 简体中文，留在当前网站
        // 更新语言链接的active状态
        updateLanguageActiveState('sc');
    }
}

// 更新语言链接的active状态
function updateLanguageActiveState(activeLang) {
    // 移除所有active类
    $('.lang a').removeClass('active');
    
    // 根据语言添加active类
    if (activeLang === 'en') {
        $('.lang a').eq(0).addClass('active');
    } else if (activeLang === 'tc') {
        $('.lang a').eq(1).addClass('active');
    } else if (activeLang === 'sc') {
        $('.lang a').eq(2).addClass('active');
    }
}
