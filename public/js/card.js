let root;
let cardGroup;
let mouseX = window.innerWidth / 2;
let mouseY = window.innerHeight / 2;
let onMouseX;
let onMouseY;
let ox;
let oy;
let devicePixel = window.devicePixelRatio;
let n = 20;
let m = 24;
let w = 500/3;
let h = 680/3;
let cw = (w+m)*n;
let ch = (h+m)*10;
//let array = [0xee7777, 0x77eedf, 0xb2ee77, 0xe477ee, 0xedee77, 0x7779ee, 0xee7777, 0x77eedf, 0xb2ee77, 0xe477ee];
//let textureArray = ['json/001_e_6999_fullset.jpeg', 'json/002_e_3913_fullset.jpeg', 'json/003_e_1597_fullset.jpeg', 'json/004_e_1971_fullset.jpeg', 'json/005_e_6918_fullset.jpeg', 'json/006_e_2381_fullset.jpeg','json/e_02127_cover.jpeg', 'json/e_09982_cover.jpeg'];
const isEn = location.href.includes('/en/');

// const ar = "/"+(isEn?'en':'tc')+"/ar.json?v=20201130&_";
// const ipo = "/"+(isEn?'en':'tc')+"/ipo.json?v=20201130&_";
// const oc = "/"+(isEn?'en':'tc')+"/oc.json?v=20201130&_";
const ar = "https://panel.cre8ir.com/api/cre8corp/ar?language="+(isEn?'en':'tc');
const ipo = "https://panel.cre8ir.com/api/cre8corp/ipo?language="+(isEn?'en':'tc');
const oc = "https://panel.cre8ir.com/api/cre8corp/oc?language="+(isEn?'en':'tc');
// const ar = "/en/ar.json?v=20201130&_";
// const ipo = "/en/ipo.json?v=20201130&_";
// const oc = "/en/oc.json?v=20201130&_";
// const arChi = "/"+(!isEn?'en':'tc')+"/ar.json?v=20201130&_";
// const ipoChi = "/"+(!isEn?'en':'tc')+"/ipo.json?v=20201130&_";
// const ocChi = "/"+(!isEn?'en':'tc')+"/oc.json?v=20201130&_";
const arChi = "https://panel.cre8ir.com/api/cre8corp/ar?language="+(!isEn?'en':'tc');
const ipoChi = "https://panel.cre8ir.com/api/cre8corp/ipo?language="+(!isEn?'en':'tc');
const ocChi = "https://panel.cre8ir.com/api/cre8corp/oc?language="+(!isEn?'en':'tc');

//const ar = "../json/ar.json";
//const ipo = "../json/ipo.json";
//const oc = "../json/oc.json";
//const arChi = "../json/ar-chi.json";
//const ipoChi = "../json/ipo-chi.json";
//const ocChi = "../json/oc-chi.json";
let arList;
let ipoList;
let ocList;
let allList = [];
let searchList = [];
let loaded = 0;
let totalCount = 0;
let dragCheck  = false;

function credentialInit(){
    insertList(ipo, 'ipo');
    insertList(ar, 'ar');
    insertList(oc, 'oc');


    insertChiList(ipoChi, 'ipo');
    insertChiList(arChi, 'ar');
    insertChiList(ocChi, 'oc');

    $(window).bind('mousemove', onCredentialMove);
    $('.credential-list').draggable({//axis: 'x',
        //cursor: 'move',  
        drag: function(e, ui){
            dragCheck = true;
            var parentPos = $('#credential-container').offset();
            var w = $('.credential-holder').width();
            var h = $('.credential-holder').height();

            //console.log(h, windowHeight);

            if (ui.position.top > parentPos.top+windowHeight*.2 && h > windowHeight) {
                ui.position.top = parentPos.top+windowHeight*.2;
            }else if(ui.position.top < windowHeight-h-windowHeight*.2 && h > windowHeight){
                ui.position.top = windowHeight-h-windowHeight*.2;
            }else if(h < windowHeight){
                ui.position.top = parentPos.top+windowHeight*.4;
            }
            if (ui.position.left > parentPos.left+windowWidth*.2) {
                ui.position.left = parentPos.left+windowWidth*.2;
            }else if(ui.position.left < windowWidth-w-windowWidth*.2){
                ui.position.left = windowWidth-w-windowWidth*.2;
            }
        },
        stop: function(){
            setTimeout(function(){
                dragCheck = false;
            }, 500);
        }
    });

    $('.filter-change').bind('click', filterChange);
    $(window).bind('wheel', onCredentialScroll);

    $('.search-btn').bind('click', searchChange);
    $('#search').click(function(){
        $('.search').addClass('active');
    });

    $('#search').blur(function(){
        $('.search').removeClass('active');
    });

    $('#search').keydown(function(e) {
      if (e.keyCode == 13) {
        searchChange();
        //alert('enter')
        return false;
      }
    });
}

function popUp(e) {
    e.preventDefault();
    if(!dragCheck){

        let chiData;
        let type = $(this).attr('data-type');
        let n = Number($(this).attr('data-count'));
        if(type == 'ar'){
            chiData = arList[n];
        }else if(type == 'ipo'){
            chiData = ipoList[n];
        }else if(type == 'oc'){
            chiData = ocList[n];
        }

        $('.pop-up').addClass('show');
        $('.pop-up').empty().append('<div class="pop-up__content"></div>');
        $('.pop-up__content').empty();
        $('.pop-up__content').append('<img src="'+$('img', this).attr('src')+'" />');
        $('.pop-up__content').append('<p>'+$(this).attr('data-name')+'<br>'+chiData.name+'</p>');
        $('.pop-up__content').append('<p class="close-btn"></p>');

        $('.pop-up__content .close-btn').on('click',function(){
            $('.pop-up').removeClass('show');
        })
        if($(this).attr('data-pdf') != 'undefined'){
            $('.pop-up__content').append('<a href="'+$(this).attr('data-pdf')+'" target="_blank">View</a>');
            //$('.pop-up__content').append('<a href="'+$(this).attr('data-pdf')+'" download target="_blank">Download</a>');
        }
    }
};

function searchChange(e){
    //e.preventDefault();

    let val = $('#search').val();
    if(val == ''){
        return false;
    }
    $('.pop-up').addClass('show');
    $('.pop-up__content').empty();
    $('.pop-up').append('<p class="close-btn"></p>');
    $('.pop-up .close-btn').on('click',function(){
        $('.pop-up').removeClass('show');
        $('.search-slider').remove();
    })
    $('.pop-up').append('<div class="swiper-container search-slider"><div class="swiper-wrapper"></div><div class="swiper-pagination"></div></div>');
    $('#search').val('');
    let tempList = [];
    let type;
    if(Number.isInteger(Number(val))){
        type = 'code';
    }else{
        type = 'name';
    }
    val = val.toLowerCase();

    for(let i=0;i<allList.length;i++){
        if(allList[i][type].toLowerCase().indexOf(val) > -1){
            $('.pop-up .swiper-wrapper').append('<div class="swiper-slide"></div>');
            $('.swiper-slide:last-child').append('<img src="'+allList[i].img+'" />');
            $('.swiper-slide:last-child').append('<p>'+allList[i].name+'</p>');
            if(allList[i].pdf){
                $('.swiper-slide:last-child').append('<a href="'+allList[i].pdf+'" target="_blank">View</a>');
                //$('.pop-up__content').append('<a href="'+$(this).attr('data-pdf')+'" download target="_blank">Download</a>');
            }
        }
    };

    var swiper = new Swiper('.search-slider', {
        speed: 800,
        slidesPerView: "auto",
        centeredSlides: true,
        spaceBetween: 30,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
    });
    //console.log(tempList);
}

function filterChange(e){
    e.preventDefault();
    $('.filter-change').removeClass('active');
    $(this).addClass('active');
    let category = $(this).attr('href');
    if($(this).attr('href') != 'all'){
        $('.credential-list a').addClass('hide');
        $('.credential-list a.'+category).removeClass('hide');
    }else{
        $('.credential-list a').removeClass('hide');
    }
    if($('.credential-list a.'+category).length < 40 && category != 'all' && windowWidth > 640){
        gsap.to($('.credential-list'), 2, {top: windowHeight*.4, left: 0, ease: Power2.easeOut});
    }else{
        gsap.to($('.credential-list'), 2, {top: 0, left: 0, ease: Power2.easeOut});
    }
}

function onCredentialScroll(e){
    if($('.ui-autocomplete').is(':hidden')){
        let sy = e.originalEvent.deltaY;
        if(sy < 0){
            var dy = $('.credential-list').position().top+400;
            if(dy > windowHeight*.2){
                dy = windowHeight*.2;
            }
        }else if(sy > 0){
            var dy = $('.credential-list').position().top-400;
            if(dy < -$('.credential-holder').height()+windowHeight*.8){
                dy = -$('.credential-holder').height()+windowHeight*.8;
            }

        }
        //$(window).unbind('wheel', onCredentialScroll);
        //gsap.kill();
        if(!dragCheck && !$('.pop-up').hasClass('show')){
            gsap.to('.credential-list', 2, {top : dy, ease: Power2.easeOut, onComplete: function(){
                //$(window).bind('wheel', onCredentialScroll);
            }});
        }
    }
}


function onCredentialMove(e){
    let x = e.clientX;
    let y = e.clientY;
    let dx = x-$(window).width()/2;
    let dy = y-$(window).height()/2;

    //let rx = dx/$(window).width()*5+10;
    let rx = x/windowWidth*10;
    let ry = y/windowHeight*10-10;

    if(!dragCheck && !$('.pop-up').hasClass('show')){
        gsap.to('.credential-list', 2, {left :-dx*.5, ease: Power2.easeOut});
        gsap.to('.camera', 2, {rotationY : rx, ease: Power2.easeOut});
        gsap.to('.camera', 2, {rotationX: -ry, ease: Power2.easeOut});
    }
}

function insertList(jsonList, type){
  $.ajax({
    type: "GET",
    url: jsonList,
    dataType: "json",
    cache: false,
    success: function(data) {
        //console.log(data);
        for(var i=0;i<data.length;i++){
            $('.credential-holder').append('<a class="'+type+'" href="#" data-type="'+type+'" data-count="'+i+'" data-pdf="'+data[i].pdf+'" data-name="'+data[i].name+'"><img src="'+data[i].img+'" /></a>');
            let b = $('.credential-holder a:last-child');
            let w = $(b).width()+40;
            let h = $(b).height()+40;
            let k = $('.credential-list a').index(b);
            let n = k%10;
            let m = Math.floor(k/10);
            $(b).addClass('anime');
            searchList.push(data[i].code);
            searchList.push(data[i].name);
            allList.push(data[i]);
            //$(b).css({'transform':'translate('+windowWidth/2-$(b).width()/2-168*n+'px '+windowHeight+'px)'});
            //$(b).css({'transform':'translate3d('+(windowWidth/2-$(b).width()/2-w*n)+'px, '+(windowHeight+$(b).height()-h*m)+'px, '+(1*k+1000)+'px)'});
            /*$(b).css({'transform':'translate3d('+(windowWidth/2-$(b).width()/2-w*n)+'px, '+(windowHeight*1.2)+'px, '+(1*k+500)+'px) rotateX(60deg)'});
            setTimeout(function(){
                $(b).addClass('anime');
                $(b).removeAttr('style');
            }, 800+50*k);*/
        }
        $('.credential-holder>a').bind('click', popUp);
        //console.log(searchList);
        $( '#search' ).autocomplete({
          source: searchList
        });
    }
  });
}

function insertChiList(jsonList, type){
  $.ajax({
    type: "GET",
    url: jsonList,
    dataType: "json",
    cache: false,
    success: function(data) {
        if(type == 'ar'){
            arList = data;
        }else if(type == 'ipo'){
            ipoList = data;
        }else if(type == 'oc'){
            ocList = data;
        }
        //console.log(data);
        for(var i=0;i<data.length;i++){
            searchList.push(data[i].name);
            allList.push(data[i]);
        }
        $( '#search' ).autocomplete({
          source: searchList
        });
    }
  });
}

function getObjects(obj, key, val) {
    var objects = [];
    for (var i in obj) {
        if (!obj.hasOwnProperty(i)) continue;
        if (typeof obj[i] == 'object') {
            objects = objects.concat(getObjects(obj[i], key, val));
        } else if (i == key && obj[key] == val) {
            objects.push(obj);
        }
    }
    return objects;
}
