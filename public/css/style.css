/* ------------------------------------------ */
/* Style naming basically follows BEM: http://getbem.com/ */
/* ------------------------------------------ */
.title--landing, .landing .lead-in {
  font-family: "FreightBig", sans-serif;
  font-size: 80px; }
  @media screen and (max-width: 768px) {
    .title--landing, .landing .lead-in {
      font-size: 50px; } }
.title--extralarge, .home-title, #news-details .news-title {
  font-family: "Montserrat", sans-serif;
  font-size: 100px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--extralarge, .home-title, #news-details .news-title {
      font-size: 50px; } }
.title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 60px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
      font-size: 40px; } }
.title--medium, .home-menu a, .slide .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 35px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium, .home-menu a, .slide .project-title {
      font-size: 22px; } }
.title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 26px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
      font-size: 26px; }
      .title--medium-small br, .about-container .txt h3 br, #news-container h3 br, .awards__sections .nomain br {
        display: none; } }
.title--small, .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--small, .project-title {
      font-size: 16px; } }

.content--common, #news-details .news-content, #news-details .news-content-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 25px;
  /*letter-spacing: 0.1em;*/ }
.content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 1.8em;
  /*letter-spacing: 0.1em;*/ }
  @media screen and (max-width: 768px) {
    .content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
      font-size: 12px; } }
.content--xsm {
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  /*letter-spacing: 0.1em;*/ }

/* BEGIN Noto Sans */
/* @font-face {
  font-family: "Gotham";
  src: url("fonts/Gotham-Book.eot?#iefix") format("embedded-opentype"), url("fonts/Gotham-Book.otf") format("opentype"), url("fonts/Gotham-Book.woff") format("woff"), url("fonts/Gotham-Book.ttf") format("truetype"), url("fonts/Gotham-Book.svg") format("svg");
  font-weight: 300;
  font-style: normal; }
@font-face {
  font-family: "Gotham";
  src: url("fonts/Gotham-Medium.eot?#iefix") format("embedded-opentype"), url("fonts/Gotham-Medium.otf") format("opentype"), url("fonts/Gotham-Medium.woff") format("woff"), url("fonts/Gotham-Medium.ttf") format("truetype"), url("fonts/Gotham-Medium.svg") format("svg");
  font-weight: 400;
  font-style: normal; }
@font-face {
  font-family: "Gotham";
  src: url("fonts/Gotham-Bold.eot?#iefix") format("embedded-opentype"), url("fonts/Gotham-Bold.otf") format("opentype"), url("fonts/Gotham-Bold.woff") format("woff"), url("fonts/Gotham-Bold.ttf") format("truetype"), url("fonts/Gotham-Bold.svg") format("svg");
  font-weight: 500;
  font-style: normal; }
@font-face {
  font-family: "FreightBig";
  src: url("fonts/FreightBig-Light.eot?#iefix") format("embedded-opentype"), url("fonts/FreightBig-Light.otf") format("opentype"), url("fonts/FreightBig-Light.woff") format("woff"), url("fonts/FreightBig-Light.ttf") format("truetype"), url("fonts/FreightBig-Light.svg") format("svg");
  font-weight: 500;
  font-style: normal; } */
/* END Noto Sans */
/* Style commonly used  */
/* ------------------------------------------ */
body,
body * {
  max-height: 1000000px; }

html {
  font-size: 16px;
  /*
  @media screen and (max-width:1580px) {
      font-size:15px;
  }
  @media screen and (max-width:991px) {
      font-size:14px;
  }
  @media screen and (max-width:767px) {
      font-size:13px;
  }
  */ }

body {
  font-size: 16px;
  /*
  @media screen and (max-width:1580px) {
      font-size:15px;
  }
  @media screen and (max-width:991px) {
      font-size:14px;
  }
  @media screen and (max-width:767px) {
      font-size:13px;
  }
  */
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  margin: 0 !important;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  position: relative;
  background-color: #FFF;
}
html[lang="tc"] body,
html[lang="sc"] body {
    font-weight: 300 !important;
}
  @media screen and (max-width: 640px) {
    body {
      font-size: 14px; } }

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

a {
  text-decoration: none;
  display: inline-block;
  color: inherit;
  outline: none; }
  a:hover, a:active {
    text-decoration: none; }
  a:focus {
    text-decoration: none;
    outline: none; }

img {
  image-rendering: -webkit-optimize-contrast;
  max-width: 100%;
  height: auto; }

br {
  display: block;
  opacity: 0;
  font-family: "Arial", sans-serif !important;
  font-size: 0;
  color: transparent;
  line-height: 1em; }

p {
  margin-top: 0; }
  p:last-child {
    margin-bottom: 0; }

.clearfix {
  content: '';
  display: table;
  width: 100%;
  height: 0;
  clear: both; }

.nowrap {
  overflow: hidden; }

.scroll-up-effect {
  opacity: 0;
  transform: translateY(20vh);
  /*visibility: hidden;*/ }
  .scroll-up-effect.animate {
    opacity: 1;
    transform: translateY(0);
    -webkit-transition: all 1.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -moz-transition: all 1.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -o-transition: all 1.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    transition: all 1.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }

#wrapper {
  position: relative;
  width: 100%;
  top: 0;
  left: 0px;
  min-height: 100vh;
  -webkit-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -moz-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -o-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
  #wrapper.active {
    left: -50%; }
    @media screen and (max-width: 640px) {
      #wrapper.active {
        left: -100%; } }
  #wrapper.last-slide {
    top: -40vh; }

.block-size {
  position: relative; }
  .block-size--full-width {
    width: 100%; }
  .block-size--half-width {
    width: 50%; }
  .block-size--full-height {
    min-height: 100vh; }
  .block-size--full-fill {
    width: 100%;
    height: 100%; }

.btn {
  position: relative;
  display: inline-block; }
  .btn--overline:after {
    content: '';
    position: absolute;
    width: 0%;
    height: 10%;
    top: 45%;
    left: -5%;
    background-color: #FFF; }
  .btn--overline:hover:after {
    width: 110%; }

#bg {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  clip-path: circle(20% at 50% -20%);
  /*@include animateAll(.8s, .5s, $easeInOutQuart);*/ }
  #bg:before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-image: url(../images/bg-day.jpg);
    background-size: cover;
    display: inline-block;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -moz-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0; }
  #bg:after {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-image: url(../images/bg-night.jpg);
    background-size: cover;
    display: inline-block;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -moz-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0; }
  #bg.transit-in {
    clip-path: circle(100%); }
  #bg.transit-out {
    clip-path: circle(20%); }
  #bg.transit-finish {
    clip-path: none; }
  #bg.day:before {
    opacity: 1; }
  #bg.night:after {
    opacity: 1; }

.index {
  position: fixed;
  width: 34vw;
  height: 34vw;
  top: 0%;
  left: 0%;
  border: solid 2px #72253d;
  border-radius: 50%;
  font-size: 16px;
  letter-spacing: .25em;
  animation: showIndex 1.5s;
  background-color: rgba(255, 255, 255, 0.1);
  /*mix-blend-mode: soft-light;*/
  backdrop-filter: blur(8px);
  -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
  .index-main {
    position: fixed;
    width: 34vw;
    height: 34vw;
    top: 0%;
    left: 50%;
    transform: translate(-50%, -90%);
    z-index: 35;
    -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
    @media screen and (max-width: 640px) {
      .index-main {
        display: none; } }
    .index-main:hover {
      z-index: 31;
      transform: translate(-50%, -80%); }
  .index:hover {
    transform: scale(1); }
    .index:hover:nth-child(2) {
      transform: translateY(5%); }
  .index--child {
    border: none;
    background-image: url(../images/circle.svg);
    background-size: cover; }
  .index--parent {
    transform: scale(0.75);
    z-index: 31;
    overflow: hidden; }
  .index .close-btn {
    position: absolute;
    width: 50px;
    height: 50px;
    left: 50%;
    bottom: 0%;
    transform: translate(-50%, 50%); }
    .index .close-btn a {
      position: relative;
      width: 100%;
      height: 100%;
      left: 0px;
      top: 0px;
      padding-top: 0px;
      background-image: url(../images/close-btn.svg);
      background-position: center;
      background-repeat: no-repeat;
      background-size: 20px 20px; }
  .index .index-holder {
    position: relative;
    width: 100%;
    height: 100%; }
  .index .index-menu {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30vw;
    height: 30vw;
    transform: translate(-50%, -50%); }
  .index a {
    position: absolute;
    width: 40%;
    top: 50%;
    left: 50%;
    padding-top: 46%;
    padding-bottom: 50px;
    color: #464646;
    transform-origin: top left;
    transform: translateX(-50%);
    display: inline-block;
    text-align: center;
    filter: grayscale(1) invert(1);
    -webkit-transition: all 0.2s ease-in-out 0s;
    -moz-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s; }
    .index a img {
      width: 100%; }
    .index a.close {
      width: 30px;
      transform: translate(-50%, -15px); }
    .index a:after {
      content: '';
      position: absolute;
      top: 91%;
      left: 50%;
      margin-left: -4px;
      width: 8px;
      height: 8px;
      background-color: #72253d;
      border-radius: 50%;
      transform: scale(0);
      -webkit-transition: all 0.3s ease-in-out 0s;
      -moz-transition: all 0.3s ease-in-out 0s;
      -o-transition: all 0.3s ease-in-out 0s;
      transition: all 0.3s ease-in-out 0s; }
    .index a.active, .index a:hover {
      color: #72253d;
      fill: #72253d;
      filter: grayscale(0) invert(0); }
      .index a.active:after, .index a:hover:after {
        transform: scale(1); }
    .index a:nth-child(2) {
      transform: rotate(-45deg) translateX(-50%); }
    .index a:nth-child(3) {
      transform: rotate(-90deg) translateX(-50%); }
    .index a:nth-child(4) {
      transform: rotate(-135deg) translateX(-50%); }
    .index a:nth-child(5) {
      transform: rotate(-180deg) translateX(-50%); }
    .index a:nth-child(6) {
      transform: rotate(-225deg) translateX(-50%); }
    .index a:nth-child(7) {
      transform: rotate(-270deg) translateX(-50%); }
    .index a:nth-child(8) {
      transform: rotate(-315deg) translateX(-50%); }
  .index .slash {
    position: absolute;
    top: 50%;
    left: 50%;
    padding-top: 50%;
    color: #FFF;
    transform-origin: top center;
    display: inline-block; }

.slide-circle-holder {
  position: relative;
  height: 100%;
  z-index: 5;
  overflow: hidden; }
  .slide-circle-holder.hover .slide-circle {
    opacity: 1; }
    .slide-circle-holder.hover .slide-circle:before {
      transform-origin: 50% 52%; }
    .slide-circle-holder.hover .slide-circle:after {
      transform-origin: 52% 50%; }
    .slide-circle-holder.hover .slide-circle:nth-child(2) {
      transform-origin: 50% 50%;
      transform: translate(-50%, -50%) scale(1.02) rotate(15deg); }
    .slide-circle-holder.hover .slide-circle:nth-child(3) {
      transform-origin: 50% 50%;
      transform: translate(-50%, -50%) scale(1.05) rotate(6deg); }
  .slide-circle-holder.hover .slide-circle:before, .slide-circle-holder.hover .slide-circle:after {
    border: solid 1px #ff6400; }

.section-slide {
  position: relative;
  width: 100%;
  margin: auto;
  height: 100vh;
  overflow: visible; }
  .section-slide .swiper-slide {
    position: relative;
    overflow: hidden; }
  .section-slide .slide-title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #464646;
    text-align: center;
    display: inline-block;
    animation: .5s fade-out linear 5s;
    animation-fill-mode: forwards; }
    .section-slide .slide-title .slide-logo {
      width: 25vw; }
      @media screen and (max-width: 640px) {
        .section-slide .slide-title .slide-logo {
          width: 50vw; } }
  .section-slide .slide-content {
    position: absolute;
    bottom: 20%;
    right: 5%;
    width: 20%;
    color: #FFF;
    display: inline-block; }
    .section-slide .slide-content:before {
      content: '/';
      position: absolute;
      top: 0px;
      left: 0px;
      transform: translateX(-200%);
      display: inline-block; }

.city-circle {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 50vw;
  height: 50vw;
  transform: translate(-50%, -50%);
  /*filter: drop-shadow(0px 2px 2px $colorOrange) drop-shadow(2px 0px 2px $colorOrange);*/
  opacity: .8; }

.slide-circle {
  position: absolute;
  left: 50%;
  top: 50%;
  /*width: 100vw;
  height: 100vw;*/
  transform: translate(0%, 0%);
  display: inline-block;
  opacity: .8;
  /*mix-blend-mode: screen;*/
  -webkit-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -moz-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -o-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
  .slide-circle:before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 50vw;
    height: 50vw;
    border: solid 1px rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%) rotate(30deg);
    transform-origin: 50% 55%;
    display: inline-block;
    animation: circleRotate linear 14s infinite; }
    @media screen and (max-width: 640px) {
      .slide-circle:before {
        width: 80vw;
        height: 80vw; } }
  .slide-circle:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 50vw;
    height: 50vw;
    border: solid 1px rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%) rotate(15deg);
    transform-origin: 55% 50%;
    display: inline-block;
    /*filter: drop-shadow(0px 2px 2px $colorRed);*/
    animation: circleRotateReverse linear 12s infinite; }
    @media screen and (max-width: 640px) {
      .slide-circle:after {
        width: 80vw;
        height: 80vw; } }
  .slide-circle:nth-child(2) {
    transform-origin: 55% 50%;
    transform: translate(-50%, -50%) scale(1.1) rotate(30deg); }
  .slide-circle:nth-child(3) {
    transform-origin: 50% 55%;
    transform: translate(-50%, -50%) scale(1.2) rotate(15deg); }

.paging {
  position: absolute;
  width: 46px;
  height: 53px;
  left: 60px;
  bottom: 15%; }
  .paging:before {
    content: '';
    width: 70px;
    height: 1px;
    background-color: #464646;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg); }
  .paging span:nth-child(2) {
    position: absolute;
    bottom: 0px;
    right: 0px; }

.video {
  position: relative; }
  .video--full {
    position: absolute;
    width: 100vw;
    height: 100vh;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); }

.content {
  position: relative;
  width: 100%;
  min-height: 100vh;
  /*clip-path: circle(20%);*/
  opacity: 0;
  -webkit-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -moz-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -o-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
  .content--child {
    position: relative;
    padding: 250px 0px;
    margin: auto 5%; }
    .content--child img {
      width: 100%; }
  .content--full {
    position: relative;
    width: 100%;
    height: 100vh; }
    @media screen and (max-width: 640px) {
      .content--full {
        height: auto; } }
    .content--full img {
      width: 100%; }
  .content .mask {
    width: 100vw;
    height: 100vh; }
    @media screen and (max-width: 640px) {
      .content .mask {
        height: auto; } }
  .content.transit-in {
    opacity: 1;
    /*clip-path: circle(100%);*/ }
  .content.transit-out {
    opacity: 0;
    /*clip-path: circle(20%);*/ }
    .content.transit-out .index {
      top: -20%; }
    .content.transit-out:after {
      content: '';
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      z-index: 100; }

.swiper-button-next {
  right: 60px;
  width: 256px;
  height: 23px;
  background-image: url(../images/arrow-right.svg);
  text-align: center;
  line-height: 23px;
  background-size: cover;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s; }
  .swiper-button-next div {
    position: relative;
    height: 23px;
    overflow: hidden;
    opacity: .3; }
  .swiper-button-next span {
    position: relative;
    display: block; }

.swiper-button-prev {
  left: 60px;
  width: 256px;
  height: 23px;
  background-image: url(../images/arrow-left.svg);
  text-align: center;
  line-height: 23px;
  background-size: cover;
  -webkit-transition: all 0.5s ease-in-out 0s;
  -moz-transition: all 0.5s ease-in-out 0s;
  -o-transition: all 0.5s ease-in-out 0s;
  transition: all 0.5s ease-in-out 0s; }
  .swiper-button-prev div {
    position: relative;
    height: 23px;
    overflow: hidden;
    opacity: .3; }
  .swiper-button-prev span {
    position: relative;
    display: block; }

.swiper-button-disabled {
  opacity: 0 !important; }

@keyframes showIndex {
  0% {
    top: -20%; }
  50% {
    top: -20%; }
  100% {
    top: 0%; } }
@keyframes circleRotate {
  0% {
    transform: translate(-50%, -50%) rotate(30deg); }
  100% {
    transform: translate(-50%, -50%) rotate(390deg); } }
@keyframes circleRotateReverse {
  0% {
    transform: translate(-50%, -50%) rotate(15deg); }
  100% {
    transform: translate(-50%, -50%) rotate(375deg); } }
.display--inline-block {
  display: inline-block; }

.display--block {
  display: inline-block; }

.display--inline {
  display: inline; }

.display--table {
  display: table; }

.display--table-cell {
  display: table-cell; }

.display--flex {
  display: flex; }

.align--hc {
  text-align: center; }
.align--hl {
  text-align: left; }
.align--hr {
  text-align: right; }
.align--vt {
  vertical-align: top; }
.align--vb {
  vertical-align: bottom; }
.align--vm {
  vertical-align: middle; }
.align--hvc {
  text-align: center;
  vertical-align: middle; }

.hidden-all {
  display: none !important; }

.overflow-hidden {
  touch-action: none;
  -webkit-overflow-scrolling: none;
  overflow: hidden;
  /* Other browsers */
  overscroll-behavior: none; }

.prevent-click:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  z-index: 30; }

.home .slide-circle-holder {
  /*mix-blend-mode: color-dodge;*/ }
.home .about-container__holder {
  color: #FFF; }
.home #news-details {
  color: #FFF; }
  .home #news-details a {
    color: #FFF; }

@keyframes fade-out {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    visibility: hidden; } }
/* END Style commonly used  */
/* ------------------------------------------ */
/* Style for menu */
/* ------------------------------------------ */
.home #header .logo {
  background-image: url(../images/logo.svg); }
.home #header .menu-btn:before {
  background-color: #FFF; }
.home #header .menu-btn:after {
  background-color: #FFF; }
.home #header .menu-holder a {
  color: #FFF; }
.home #header .sound-btn {
  background-image: url(../images/sound-off-w.svg); }
  .home #header .sound-btn.sound-on {
    background-image: url(../images/sound-on-w.svg); }
.home .bg-mode__btn {
  border: solid 1px #FFF; }
.home .bg-mode__btn:before {
  background-color: #FFF; }

#header {
  position: fixed;
  width: 100%;
  top: 0%;
  left: 0px;
  padding: 35px 60px;
  /*mix-blend-mode: exclusion;*/
  z-index: 30;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -moz-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s; }
  @media screen and (max-width: 640px) {
    #header {
      text-align: left;
      padding: 20px 15px; } }
  #header .logo {
    position: relative;
    width: 160px;
    height: 50px;
    background-image: url(../images/logo-b.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    z-index: 1; }
    @media screen and (max-width: 640px) {
      #header .logo {
        width: 120px;
        height: 38px;
        background-size: 100% 100%; } }
  #header .menu-holder {
    position: absolute;
    top: 35px;
    right: 60px; }
    @media screen and (max-width: 640px) {
      #header .menu-holder {
        top: 20px;
        right: 15px; } }
    #header .menu-holder a {
      color: #464646;
      display: inline-block;
      vertical-align: middle;
      margin-right: 70px; }
      @media screen and (max-width: 640px) {
        #header .menu-holder a {
          margin-right: 40px; } }
    #header .menu-holder .bg-mode {
      width: 100%;
      min-width: 200px;
      margin-top: 10px;
      padding-right: 40px; }
  #header .sound-btn {
    position: absolute;
    bottom: 0px;
    right: 0px;
    width: 30px;
    height: 25px;
    cursor: pointer;
    background-image: url(../images/sound-off-b.svg);
    background-repeat: no-repeat;
    background-size: cover; }
    #header .sound-btn.sound-on {
      background-image: url(../images/sound-on-b.svg); }
  #header .menu-btn {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 25px;
    height: 15px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 50px;
    cursor: pointer;
    z-index: 20;
    -webkit-transition: all 0.2s ease 0s;
    -moz-transition: all 0.2s ease 0s;
    -o-transition: all 0.2s ease 0s;
    transition: all 0.2s ease 0s; }
    @media screen and (max-width: 640px) {
      #header .menu-btn {
        right: 0px; } }
    #header .menu-btn:hover {
      top: -3px;
      height: 21px; }
    #header .menu-btn:before {
      position: absolute;
      top: 0px;
      left: 0%;
      content: '';
      width: 100%;
      height: 3px;
      border-radius: 2px;
      background-color: #464646;
      transform-origin: center;
      /*@include animateAll(.2s, 0s);*/ }
    #header .menu-btn:after {
      position: absolute;
      bottom: 0px;
      left: 0%;
      content: '';
      width: 100%;
      height: 3px;
      border-radius: 2px;
      background-color: #464646;
      transform-origin: center;
      /*@include animateAll(.2s, 0s);*/ }

.bg-mode {
  width: 50%;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer; }
  .bg-mode__btn {
    position: relative;
    width: 85%;
    height: 6px;
    border: solid 1px #464646;
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle; }
    .bg-mode__btn:before {
      content: '';
      position: relative;
      left: 0%;
      width: 50%;
      height: 100%;
      border-radius: 3px;
      background-color: #464646;
      display: inline-block;
      -webkit-transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      -moz-transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      -o-transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
  .bg-mode:after {
    content: '';
    position: relative;
    width: 18px;
    height: 18px;
    background-image: url(../images/brightless.svg);
    display: inline-block;
    vertical-align: middle; }
  .bg-mode.dark .bg-mode__btn:before {
    left: 50%; }
  .bg-mode.dark:after {
    background-image: url(../images/brightless-2.svg); }

.menu-mask {
  position: fixed;
  top: 0px;
  right: 0px;
  width: 0%;
  height: 100%;
  background-color: #72253d;
  clip-path: circle(10% at 100% 0%);
  visibility: hidden;
  overflow: hidden;
  z-index: 170;
  -webkit-transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -moz-transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -o-transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
  .menu-mask.active {
    width: 100%;
    height: 100%;
    visibility: visible;
    clip-path: circle(100% at 50% 50%); }

.menu {
  position: relative;
  width: 100vw;
  height: 100vh;
  padding: 80px 100px;
  box-sizing: border-box;
  top: 0px;
  left: 0%;
  display: flex;
  background-color: #72253d;
  overflow: hidden;
  justify-content: space-between;
  align-items: flex-start;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0.6s;
  -moz-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0.6s;
  -o-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0.6s;
  transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0.6s; }
  @media screen and (max-width: 640px) {
    .menu {
      padding: 15px;
      flex-wrap: wrap; } }
  .menu .close-btn {
    position: absolute;
    top: 40px;
    right: 100px;
    width: 30px;
    height: 30px;
    background-image: url(../images/close-btn.svg);
    background-size: cover;
    cursor: pointer;
    z-index: 10;
    -webkit-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -moz-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -o-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
    @media screen and (max-width: 640px) {
      .menu .close-btn {
        top: 15px;
        right: 15px; } }
    .menu .close-btn:hover {
      transform: rotate(270deg); }
  .menu .logo--menu {
    position: relative;
    width: 200px;
    height: 60px;
    background-image: url(../images/logo.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    z-index: 1;
    margin-top: 20px;
    margin-right: 10%; }
    @media screen and (max-width: 640px) {
      .menu .logo--menu {
        width: 120px;
        height: 38px;
        background-size: 100% 100%;
        margin-top: 0px;
        margin-right: 0%;
        margin: .5em 0; } }
  .menu .bg-mode:after {
    background-image: url(../images/brightless-w.svg); }
  .menu .bg-mode.dark:after {
    background-image: url(../images/brightless-2.svg); }
  .menu .bg-mode__btn {
    border: solid 1px #FFF; }
    .menu .bg-mode__btn:before {
      background-color: #FFF; }
  .menu .main-menu {
    position: relative;
    width: 70%;
    -webkit-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -moz-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -o-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
    @media screen and (max-width: 640px) {
      .menu .main-menu {
        right: -100%;
        width: 100%;
        margin-left: 0%; } }
    .menu .main-menu:before {
      content: '';
      display: block;
      width: 100%;
      height: 15%; }
    .menu .main-menu a {
      color: #FFF;
      display: inline-block;
      -webkit-text-stroke: 0.5px #FFF;
      -webkit-text-fill-color: transparent;
      -webkit-background-clip: text;
      -webkit-transition: all 0.2s ease-in-out 0s;
      -moz-transition: all 0.2s ease-in-out 0s;
      -o-transition: all 0.2s ease-in-out 0s;
      transition: all 0.2s ease-in-out 0s;
      vertical-align: middle; }
      .menu .main-menu a:hover, .menu .main-menu a.active {
        background-color: #FFF; }
    .menu .main-menu ul {
      position: relative;
      width: 100%;
      list-style: none; }
      .menu .main-menu ul li {
        display: block;
        margin: 10px auto; }
      .menu .main-menu ul a {
        font-weight: 500; }
        @media screen and (max-width: 640px) {
          .menu .main-menu ul a {
            font-size: 35px !important; } }
  .menu.active {
    visibility: visible;
    opacity: 1;
    /*background-color: rgba(0,0,0,.7);*/ }
    .menu.active .main-menu {
      right: 0px; }
  .menu .sns-holder {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    width: 40px;
    height: 100%;
    align-content: flex-end; }
    @media screen and (max-width: 640px) {
      .menu .sns-holder {
        width: 100%;
        height: auto; } }
  .menu .sns-btn {
    width: 30px;
    height: 30px;
    margin: 5px auto;
    display: inline-block; }
    @media screen and (max-width: 640px) {
      .menu .sns-btn {
        margin: 5px 10px; } }
  .menu .sns-in {
    background-image: url(../images/sns-01-w.svg); }
  .menu .sns-fb {
    background-image: url(../images/sns-02-w.svg); }
  .menu .sns-wc {
    background-image: url(../images/sns-03-w.svg); }

.side-menu {
  position: relative;
  width: 50vw;
  display: flex;
  align-items: center;
  color: #FFF;
  margin-bottom: 30px;
  line-height: 25px; }
  @media screen and (max-width: 640px) {
    .side-menu {
      width: 100%; } }
  .side-menu span {
    display: inline-block;
    margin: auto 20px; }
    @media screen and (max-width: 640px) {
      .side-menu span {
        margin: auto 10px; } }
  .side-menu a {
    margin: auto 10px; }
  .side-menu .sound-btn {
    width: 30px;
    height: 25px;
    cursor: pointer;
    background-image: url(../images/sound-off-w.svg);
    background-repeat: no-repeat;
    background-size: cover; }
    .side-menu .sound-btn.sound-on {
      background-image: url(../images/sound-on-w.svg); }

/* END Style menu  */
/* ------------------------------------------ */
/* Style for menu */
/* ------------------------------------------ */
.home #footer {
  color: #FFF; }
  .home #footer a {
    color: #FFF; }
  .home #footer .sns-in {
    background-image: url(../images/sns-01-w.svg); }
  .home #footer .sns-fb {
    background-image: url(../images/sns-02-w.svg); }
  .home #footer .sns-wc {
    background-image: url(../images/sns-03-w.svg); }

#footer {
  position: relative;
  bottom: 0px;
  left: 0px;
  width: 100%;
  color: #464646;
  padding: 35px 60px;
  transform: translateY(-100%);
  /*mix-blend-mode: exclusion;*/
  display: flex;
  flex-wrap: wrap;
  z-index: 20; }
  @media screen and (max-width: 640px) {
    #footer {
      padding: 5px 15px;
      flex-direction: column-reverse; } }
  #footer .footer--col {
    position: relative;
    width: 50%; }
    @media screen and (max-width: 640px) {
      #footer .footer--col {
        width: 100%;
        margin: .5em auto; } }
    #footer .footer--col p {
      display: inline-block; }
    #footer .footer--col:nth-child(2) {
      text-align: right; }
      @media screen and (max-width: 640px) {
        #footer .footer--col:nth-child(2) {
          text-align: left; } }
  #footer .lang {
    display: inline-block; }
    @media screen and (max-width: 640px) {
      #footer .lang {
        display: none; } }
  #footer .sns-btn {
    width: 25px;
    height: 25px;
    display: inline-block; }
    #footer .sns-btn:nth-child(3) {
      margin-right: 50px; }
  #footer .sns-in {
    background-image: url(../images/sns-01-b.svg); }
  #footer .sns-fb {
    background-image: url(../images/sns-02-b.svg); }
  #footer .sns-wc {
    background-image: url(../images/sns-03-b.svg); }
  #footer a {
    color: #464646;
    display: inline-block;
    vertical-align: middle;
    margin: auto 2px; }

/* END Style menu  */
/* ------------------------------------------ */
.title--landing, .landing .lead-in {
  font-family: "FreightBig", sans-serif;
  font-size: 80px; }
  @media screen and (max-width: 768px) {
    .title--landing, .landing .lead-in {
      font-size: 50px; } }
.title--extralarge, .home-title, #news-details .news-title {
  font-family: "Montserrat", sans-serif;
  font-size: 100px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--extralarge, .home-title, #news-details .news-title {
      font-size: 50px; } }
.title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 60px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
      font-size: 40px; } }
.title--medium, .home-menu a, .slide .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 35px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium, .home-menu a, .slide .project-title {
      font-size: 22px; } }
.title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 26px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
      font-size: 26px; }
      .title--medium-small br, .about-container .txt h3 br, #news-container h3 br, .awards__sections .nomain br {
        display: none; } }
.title--small, .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--small, .project-title {
      font-size: 16px; } }

.content--common, #news-details .news-content, #news-details .news-content-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 25px;
  /*letter-spacing: 0.1em;*/ }
.content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 1.8em;
  /*letter-spacing: 0.1em;*/ }
  @media screen and (max-width: 768px) {
    .content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
      font-size: 12px; } }
.content--xsm {
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  /*letter-spacing: 0.1em;*/ }

/* ------------------------------------------ */
.landing-content {
  position: relative; }
  @media screen and (max-width: 640px) {
    .landing-content {
      width: 100%;
      height: 100%;
      overflow: hidden; } }
  .landing-content #wrapper {
    min-height: inherit !important;
    height: 100%; }

.landing {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden; }
  @media screen and (max-width: 640px) {
    .landing {
      height: 100%; } }
  .landing__holder {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex; }
    @media screen and (max-width: 640px) {
      .landing__holder {
        flex-wrap: wrap;
        /*height: 100vh;*/ } }
  .landing .entrance {
    position: relative;
    width: 50%;
    height: 100%;
    display: block;
    background-size: cover;
    overflow: hidden;
    -webkit-transition: all 1s cubic-bezier(0.075, 0.82, 0.165, 1) 0s;
    -moz-transition: all 1s cubic-bezier(0.075, 0.82, 0.165, 1) 0s;
    -o-transition: all 1s cubic-bezier(0.075, 0.82, 0.165, 1) 0s;
    transition: all 1s cubic-bezier(0.075, 0.82, 0.165, 1) 0s; }
    .landing .entrance:hover {
      width: 80%; }
      @media screen and (max-width: 640px) {
        .landing .entrance:hover {
          width: 100%; } }
    @media screen and (max-width: 640px) {
      .landing .entrance {
        width: 100%;
        height: 50%; } }
    .landing .entrance video {
      position: absolute;
      top: 0px;
      left: 50%;
      height: 100%;
      transform: translateX(-50%);
      opacity: .85;
      visibility: hidden; }
      .landing .entrance video.show {
        visibility: visible; }
    .landing .entrance img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
      animation: fadeIn 1s ease-in-out;
      animation-fill-mode: forwards;
      animation-delay: 4.5s; }
      @media screen and (max-width: 640px) {
        .landing .entrance img {
          width: 60%; } }
  .landing .lead-in {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    text-align: center;
    color: #FFF;
    transform: translateX(2%);
    animation: fadeInOnce 5s ease-in-out;
    animation-fill-mode: forwards; }
    .landing .lead-in h2 {
      position: relative;
      font-weight: 300;
      top: 50%;
      transform: translateY(-50%); }
      .landing .lead-in h2 span {
        margin: auto 0.2em;
        opacity: 0; }
        @media screen and (max-width: 640px) {
          .landing .lead-in h2 span {
            display: block; } }
        .landing .lead-in h2 span:nth-child(1) {
          animation: fadeIn 1s ease-in-out;
          animation-fill-mode: forwards; }
        .landing .lead-in h2 span:nth-child(2) {
          animation: fadeIn 1s ease-in-out;
          animation-fill-mode: forwards;
          animation-delay: 0.5s; }
        .landing .lead-in h2 span:nth-child(3) {
          animation: fadeIn 1s ease-in-out;
          animation-fill-mode: forwards;
          animation-delay: 1s; }
        .landing .lead-in h2 span:nth-child(4) {
          animation: fadeIn 1s ease-in-out;
          animation-fill-mode: forwards;
          animation-delay: 1.5s; }
        .landing .lead-in h2 span:nth-child(5) {
          animation: fadeIn 1s ease-in-out;
          animation-fill-mode: forwards;
          animation-delay: 2s; }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  10% {
    opacity: 0; }
  100% {
    opacity: 1; } }
@keyframes fadeInOnce {
  0% {
    opacity: 0; }
  20% {
    opacity: 1; }
  80% {
    opacity: 1; }
  100% {
    opacity: 0;
    visibility: hidden; } }
/* END Style ring-design used  */
/* ------------------------------------------ */
.title--landing, .landing .lead-in {
  font-family: "FreightBig", sans-serif;
  font-size: 80px; }
  @media screen and (max-width: 768px) {
    .title--landing, .landing .lead-in {
      font-size: 50px; } }
.title--extralarge, .home-title, #news-details .news-title {
  font-family: "Montserrat", sans-serif;
  font-size: 100px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--extralarge, .home-title, #news-details .news-title {
      font-size: 50px; } }
.title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 60px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
      font-size: 40px; } }
.title--medium, .home-menu a, .slide .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 35px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium, .home-menu a, .slide .project-title {
      font-size: 22px; } }
.title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 26px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
      font-size: 26px; }
      .title--medium-small br, .about-container .txt h3 br, #news-container h3 br, .awards__sections .nomain br {
        display: none; } }
.title--small, .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--small, .project-title {
      font-size: 16px; } }

.content--common, #news-details .news-content, #news-details .news-content-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 25px;
  /*letter-spacing: 0.1em;*/ }
.content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 1.8em;
  /*letter-spacing: 0.1em;*/ }
  @media screen and (max-width: 768px) {
    .content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
      font-size: 12px; } }
.content--xsm {
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  /*letter-spacing: 0.1em;*/ }

/* ------------------------------------------ */
#wrapper.home {
  height: 100%;
  min-height: inherit !important;
  /*height: 140vh;*/ }

.video-background {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0%;
  left: 0%;
  transform: translate(0%, 0%);
  clip-path: circle(20% at 50% -20%);
  animation: 1.5s enlarge cubic-bezier(0.77, 0, 0.175, 1) 0.7s;
  animation-fill-mode: forwards;
  overflow: hidden; }
  .video-background video {
    position: relative;
    top: 50%;
    left: 50%;
    width: auto;
    min-width: 100%;
    transform: translate(-50%, -50%); }
    @media (orientation: portrait) {
      .video-background video {
        width: auto;
        height: 100%; } }
  .video-background:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    background-color: #000;
    opacity: .4; }

.ani-enlarge {
  animation: 1s enlarge cubic-bezier(0.77, 0, 0.175, 1);
  animation-fill-mode: forwards; }

.ani-shink {
  animation: 1s shink cubic-bezier(0.77, 0, 0.175, 1);
  animation-fill-mode: forwards; }

@keyframes enlarge {
  0% {
    clip-path: circle(20% at 50% -20%); }
  99% {
    clip-path: circle(100%); }
  100% {
    clip-path: none; } }
@keyframes shink {
  0% {
    clip-path: circle(100% at 50% 50%); }
  100% {
    clip-path: circle(20% to 50% -20%); } }
.home-menu {
  position: absolute;
  width: 50%;
  list-style: none;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  text-align: center;
  /*mix-blend-mode: overlay;*/ }
  .home-menu li {
    position: relative;
    display: inline-block;
    margin: 0px auto;
    line-height: 3em;
    opacity: .7; }
    .home-menu li:before {
      content: '';
      position: absolute;
      width: 0%;
      height: 100%;
      top: 0px;
      left: 0px;
      background-color: #72253d;
      opacity: 1;
      -webkit-transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
      -moz-transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
      -o-transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
      transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
    .home-menu li:hover {
      opacity: 1; }
      .home-menu li:hover:before {
        width: 100%; }
  .home-menu a {
    position: relative;
    font-weight: 500;
    color: #FFF;
    display: inline-block;
    /*-webkit-text-stroke: .5px $colorWht;
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;*/
    -webkit-transition: all 0.2s ease-in-out 0s;
    -moz-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    vertical-align: middle; }
    .home-menu a:hover, .home-menu a.active {
      /*-webkit-text-stroke: .5px $colorWht;
      -webkit-text-fill-color: transparent;
      -webkit-background-clip: text;
      background-color: $colorWht;*/ }

.home-title {
  font-weight: 500;
  text-transform: uppercase;
  position: absolute;
  bottom: 15%;
  left: 60px;
  color: #FFF;
  visibility: hidden;
  overflow: hidden;
  opacity: 0;
  transform: translateY(0%);
  -webkit-transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -moz-transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -o-transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
  @media screen and (max-width: 640px) {
    .home-title {
      font-size: 70px !important;
      letter-spacing: 4px !important;
      width: 100%;
      left: 0px;
      top: 65%;
      bottom: auto;
      padding: 0px 15px;
      transform: translateY(-20%); } }
  .home-title.active {
    visibility: visible;
    opacity: 1; }
  .home-title #messenger {
    margin-left: .4em;
    display: block;
    word-break: break-word; }
    @media screen and (max-width: 640px) {
      .home-title #messenger {
        margin-left: 0px;
        height: 200px; } }
  .home-title p {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    overflow: hidden;
    -webkit-text-stroke: 1px #FFF;
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    -webkit-transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -moz-transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -o-transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
    .home-title p.active {
      background-color: #FFF; }
    .home-title p span {
      position: relative;
      top: 0%;
      left: 0%;
      transform: translateX(-100%);
      display: inline-block;
      -webkit-text-stroke: 1px #FFF;
      -webkit-text-fill-color: transparent;
      -webkit-background-clip: text;
      background-color: #FFF; }
  .home-title__second {
    position: relative;
    display: inline-block; }
    .home-title__second:before {
      position: absolute;
      top: .13em;
      left: 0px;
      content: '|';
      display: inline-block;
      vertical-align: middle;
      font-size: 80%;
      transform: scale(0.5, 1); }
      @media screen and (max-width: 640px) {
        .home-title__second:before {
          display: none; } }

@media screen and (max-width: 640px) {
  .last-slide .home-slide {
    transform: translateY(-40vh); } }

.home-slide {
  position: relative;
  width: 100vw;
  height: 100vh;
  -webkit-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -moz-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  -o-transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s;
  transition: all 0.6s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
  @media screen and (max-width: 640px) {
    .home-slide {
      position: fixed;
      top: 0px;
      height: 100%; } }

.slide {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  visibility: hidden;
  /*&:after {
  	content: '';
  	position: absolute;
  	width: 100%;
  	height: 100%;
  	background-color: rgba(0,0,0,0.3);
  }*/ }
  .slide.active {
    visibility: visible;
    z-index: 10; }
  .slide.visible {
    visibility: visible; }
  .slide__thu {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    transform: scale(1.1); }
  .slide .project-title {
    position: absolute;
    top: 50%;
    width: 100%;
    text-align: center;
    color: #FFF;
    transform: translateY(-50%);
    z-index: 1;
    -webkit-touch-callout: none;
    /* iOS Safari */
    -webkit-user-select: none;
    /* Safari */
    -khtml-user-select: none;
    /* Konqueror HTML */
    -moz-user-select: none;
    /* Old versions of Firefox */
    -ms-user-select: none;
    /* Internet Explorer/Edge */
    user-select: none; }
    @media screen and (max-width: 640px) {
      .slide .project-title {
        padding: 0px 30px; } }
    .slide .project-title:after {
      content: '';
      position: absolute;
      width: 53px;
      height: 87px;
      left: 50%;
      top: 50%;
      opacity: 0;
      transform: translate(-50%, -50%);
      background-image: url(../images/logo/play-btn.svg);
      background-size: cover;
      visibility: hidden;
      -webkit-transition: all 0.2s ease-in-out 0s;
      -moz-transition: all 0.2s ease-in-out 0s;
      -o-transition: all 0.2s ease-in-out 0s;
      transition: all 0.2s ease-in-out 0s; }
    .slide .project-title:hover h2 {
      opacity: 0; }
    .slide .project-title:hover:after {
      visibility: visible;
      opacity: 1; }
    @media screen and (max-width: 640px) {
      .slide .project-title:hover h2 {
        opacity: 1; }
      .slide .project-title:hover:after {
        visibility: hidden;
        opacity: 0; } }
    .slide .project-title h2 {
      -webkit-transition: all 0.2s ease-in-out 0s;
      -moz-transition: all 0.2s ease-in-out 0s;
      -o-transition: all 0.2s ease-in-out 0s;
      transition: all 0.2s ease-in-out 0s;
      text-transform: uppercase;
      opacity: 1;
      -webkit-touch-callout: none;
      /* iOS Safari */
      -webkit-user-select: none;
      /* Safari */
      -khtml-user-select: none;
      /* Konqueror HTML */
      -moz-user-select: none;
      /* Old versions of Firefox */
      -ms-user-select: none;
      /* Internet Explorer/Edge */
      user-select: none; }

/* END Style ring-design used  */
/* ------------------------------------------ */
.title--landing, .landing .lead-in {
  font-family: "FreightBig", sans-serif;
  font-size: 80px; }
  @media screen and (max-width: 768px) {
    .title--landing, .landing .lead-in {
      font-size: 50px; } }
.title--extralarge, .home-title, #news-details .news-title {
  font-family: "Montserrat", sans-serif;
  font-size: 100px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--extralarge, .home-title, #news-details .news-title {
      font-size: 50px; } }
.title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 60px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
      font-size: 40px; } }
.title--medium, .home-menu a, .slide .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 35px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium, .home-menu a, .slide .project-title {
      font-size: 22px; } }
.title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 26px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
      font-size: 26px; }
      .title--medium-small br, .about-container .txt h3 br, #news-container h3 br, .awards__sections .nomain br {
        display: none; } }
.title--small, .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--small, .project-title {
      font-size: 16px; } }

.content--common, #news-details .news-content, #news-details .news-content-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 25px;
  /*letter-spacing: 0.1em;*/ }
.content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 1.8em;
  /*letter-spacing: 0.1em;*/ }
  @media screen and (max-width: 768px) {
    .content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
      font-size: 12px; } }
.content--xsm {
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  /*letter-spacing: 0.1em;*/ }

/* ------------------------------------------ */
.home .about-container .layer-blk {
  display: none; }
.home .about-container .layer-wht {
  display: block; }

.about-container {
  position: relative;
  width: 100%;
  padding-bottom: 50px;
  min-height: 100vh;
  /*overflow: scroll;*/ }
  .about-container__whois {
    position: relative;
    width: 100%;
    display: flex;
    flex-wrap: wrap; }
    .about-container__whois .visual {
      position: fixed;
      top: 0px;
      left: 0px;
      width: 35vw;
      height: 100vh;
      overflow: hidden;
      background-image: url(../images/about/contact.jpg);
      background-size: cover; }
      @media screen and (max-width: 640px) {
        .about-container__whois .visual {
          position: relative;
          width: 100%;
          height: 50vh; } }
    .about-container__whois .content-txt {
      position: relative;
      width: 65vw;
      margin-left: 35vw;
      box-sizing: border-box;
      padding: 100px 100px; }
      @media screen and (max-width: 640px) {
        .about-container__whois .content-txt {
          width: 100%;
          margin-left: 0px;
          padding: 50px 15px; } }
      .about-container__whois .content-txt p {
        margin: 2em 0; }
    .about-container__whois .map {
      width: 100%;
      height: 50vh;
      border: none; }
  .about-container__holder {
    position: relative;
    width: 100vw;
    min-height: 100vh; }
    @media screen and (max-width: 640px) {
      .about-container__holder {
        padding: 80px 0px; } }
  .about-container__section {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    padding: 100px 0px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-evenly; }
    @media screen and (max-width: 640px) {
      .about-container__section {
        padding: 0px 0px;
        padding-top: 50px;
        min-height: auto; } }
    .about-container__section:nth-child(2n) {
      flex-direction: row-reverse; }
  .about-container__photo {
    position: relative;
    top: 100vh;
    width: 100vw;
    min-height: 100vh; }
  .about-container .txt {
    position: relative;
    width: 25%; }
    @media screen and (max-width: 640px) {
      .about-container .txt {
        width: 100%;
        padding: 20px 15px; } }
    .about-container .txt:before {
      content: '/';
      position: absolute;
      top: 0px;
      left: 0px;
      transform: translateX(-200%);
      display: inline-block; }
    .about-container .txt h3 {
      color: #72253d;
      fint-weight: normal;
      margin-bottom: 5px; }
    .about-container .txt p {
      margin: 1em 0; }
    .about-container .txt a {
      color: inherit;
      text-decoration: underline; }
  .about-container .logo {
    position: absolute;
    width: 25%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); }
  .about-container .layer {
    position: relative;
    width: 50%;
    opacity: 0;
    -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    visibility: hidden; }
    @media screen and (max-width: 640px) {
      .about-container .layer {
        width: 90%;
        padding: 20px 15px; } }
    .about-container .layer.visible {
      opacity: 1;
      visibility: visible; }
  .about-container .layer-wht {
    display: none; }
  .about-container .txt-1 {
    top: 40%;
    left: 50%;
    width: 40%;
    transform: translate(0%, -50%); }
  .about-container .txt-2 {
    top: 65%;
    left: 60%;
    width: 30%;
    transform: translate(0%, -50%); }
  .about-container .txt-3 {
    top: 40%;
    left: 15%;
    width: 25%; }
  .about-container .txt-4 {
    top: 5%;
    left: 60%;
    width: 20%; }
  .about-container .txt-5 {
    top: 15%;
    left: 15%;
    width: 20%; }
  .about-container .txt-6 {
    bottom: 15%;
    left: 25%;
    width: 25%; }
  .about-container .layer-1-1 {
    width: 60%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%); }
  .about-container .layer-1-2 {
    width: 60%;
    left: 50%;
    top: 70%;
    transform: translate(-50%, -50%); }
  .about-container .layer-2-1 {
    height: 25%;
    top: 10%;
    left: 35%; }
  .about-container .layer-2-2 {
    width: 20%;
    top: 0%;
    left: 15%; }
  .about-container .layer-2-3 {
    width: 40%;
    top: 35%;
    left: 40%; }
  .about-container .layer-2-4 {
    width: 20%;
    top: 50%;
    left: 50%; }
  .about-container .layer-3-1 {
    width: 100%;
    bottom: 0%;
    left: -20%; }
  .about-container .layer-3-2 {
    width: 50%;
    top: 5%;
    left: 40%; }
  .about-container .layer-3-3 {
    height: 30%;
    bottom: 15%;
    left: 20%; }
  .about-container .layer-3-4 {
    width: 30%;
    top: 5%;
    left: 25%; }
  .about-container .layer-4-1 {
    height: 20%;
    top: 20%;
    left: 0%; }
  .about-container .layer-4-2 {
    width: 55%;
    top: 0%;
    left: 15%; }
  .about-container .layer-4-3 {
    height: 45%;
    bottom: 10%;
    left: 10%; }
  .about-container .layer-4-4 {
    width: 20%;
    top: 5%;
    right: 0%; }
  .about-container .layer-5-1 {
    height: 50%;
    top: 25%;
    left: 25%; }
  .about-container .layer-5-2 {
    width: 50%;
    top: 5%;
    left: 40%; }
  .about-container .layer-5-3 {
    height: 100%;
    top: 5%;
    left: 20%; }
  .about-container .layer-5-4 {
    width: 40%;
    top: 50%;
    left: 15%; }
  .about-container .layer-5-5 {
    width: 25%;
    bottom: 15%;
    left: 10%; }
  .about-container .layer-6-1 {
    width: 50%;
    top: 10%;
    left: 25%; }
  .about-container .layer-6-2 {
    height: 80%;
    top: 0%;
    left: 52%; }
  .about-container .layer-6-3 {
    width: 20%;
    bottom: 5%;
    left: 0%; }
  .about-container .layer-7-1 {
    top: 100%;
    left: 20%;
    width: 40%;
    height: 40%;
    overflow: hidden; }
    .about-container .layer-7-1 img {
      max-width: none;
      height: 100%; }
  .about-container .layer-7-2 {
    top: 140%;
    left: 40%;
    width: 20%;
    height: 50%;
    margin-top: 20px;
    overflow: hidden; }
    .about-container .layer-7-2 img {
      max-width: none;
      height: 100%; }
  .about-container .layer-7-3 {
    top: 110%;
    left: 60%;
    width: 20%;
    height: 50%;
    margin-left: 20px;
    overflow: hidden; }
    .about-container .layer-7-3 img {
      max-width: none;
      height: 100%; }
  .about-container .contact-title {
    position: relative;
    left: 15%;
    width: 20%; }
  .about-container .layer-8-1 {
    width: 50%;
    top: 10%;
    left: 30%; }
  .about-container .layer-8-2 {
    margin: 20px 0;
    width: 30%;
    height: 30%;
    top: 70%;
    left: 15%;
    border: none; }
  .about-container .txt-7 {
    position: absolute;
    top: 80%;
    left: 50%;
    margin-top: 20px; }
  .about-container .facilities {
    margin-bottom: 100vh; }
  .about-container .facilities-slider {
    position: relative;
    width: 100%;
    margin-top: 100px; }
    @media screen and (max-width: 640px) {
      .about-container .facilities-slider {
        margin-top: 30px; } }

.swiper-slide {
  position: relative;
  width: 90%;
  max-width: 600px; }
  .swiper-slide:before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    -webkit-transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -moz-transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    -o-transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s;
    transition: all 0.8s cubic-bezier(0.77, 0, 0.175, 1) 0s; }

.swiper-slide-active:before {
  background-color: rgba(0, 0, 0, 0); }

.swiper-pagination-bullet-active {
  background-color: #72253d; }

.swiper-pagination {
  bottom: 0px !important; }

.swiper-wrapper {
  padding-bottom: 50px; }

/* END Style ring-design used  */
/* ------------------------------------------ */
.title--landing, .landing .lead-in {
  font-family: "FreightBig", sans-serif;
  font-size: 80px; }
  @media screen and (max-width: 768px) {
    .title--landing, .landing .lead-in {
      font-size: 50px; } }
.title--extralarge, .home-title, #news-details .news-title {
  font-family: "Montserrat", sans-serif;
  font-size: 100px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--extralarge, .home-title, #news-details .news-title {
      font-size: 50px; } }
.title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 60px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
      font-size: 40px; } }
.title--medium, .home-menu a, .slide .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 35px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium, .home-menu a, .slide .project-title {
      font-size: 22px; } }
.title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 26px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
      font-size: 26px; }
      .title--medium-small br, .about-container .txt h3 br, #news-container h3 br, .awards__sections .nomain br {
        display: none; } }
.title--small, .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--small, .project-title {
      font-size: 16px; } }

.content--common, #news-details .news-content, #news-details .news-content-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 25px;
  /*letter-spacing: 0.1em;*/ }
.content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 1.8em;
  /*letter-spacing: 0.1em;*/ }
  @media screen and (max-width: 768px) {
    .content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
      font-size: 12px; } }
.content--xsm {
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  /*letter-spacing: 0.1em;*/ }

/* ------------------------------------------ */
.home .filter {
  color: #FFF; }

#credential-container {
  position: relative;
  width: 100vw;
  /*padding: 200px 0px;*/
  box-sizing: border-box;
  transform-style: preserve-3d;
  /*perspective-origin:50% 50%;*/
  perspective: 1000px;
  overflow: hidden; }

.pop-up {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(6px);
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -moz-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -o-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
  .pop-up.show {
    opacity: 1;
    visibility: visible; }
  .pop-up__content {
    position: absolute;
    width: 20vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center; }
    @media screen and (max-width: 640px) {
      .pop-up__content {
        width: 80vw; } }
    .pop-up__content img {
      width: 100%;
      box-shadow: 5px 5px 5px #CCC; }
    .pop-up__content p {
      margin: 10px auto;
      padding: 10px 0px;
      border-bottom: solid 1px #000; }
    .pop-up__content a {
      width: 40%;
      display: inline-block;
      padding: 10px;
      vertical-align: middle;
      background-color: #000;
      border-radius: 5px;
      margin: auto 5px;
      color: #FFF; }
    .pop-up__content .close-btn {
      top: 0px !important;
      right: 0px !important; }
  .pop-up .close-btn {
    position: absolute;
    top: 15%;
    right: 90px;
    width: 30px;
    height: 30px;
    background-image: url(../images/close-btn.svg);
    background-size: 60%;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 50%;
    background-color: #000;
    margin: auto;
    padding: 0px;
    transform: translate(50%, -50%);
    cursor: pointer; }
  .pop-up .search-slider {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 0px;
    transform: translateY(-50%); }
  .pop-up .swiper-slide {
    width: 15vw;
    text-align: center; }
    .pop-up .swiper-slide:before {
      display: none; }
    .pop-up .swiper-slide p {
      margin: 10px auto;
      padding: 10px 0px;
      border-bottom: solid 1px #000; }
    .pop-up .swiper-slide a {
      width: 40%;
      display: inline-block;
      padding: 10px;
      vertical-align: middle;
      background-color: #000;
      border-radius: 5px;
      margin: auto 5px;
      color: #FFF; }

.camera {
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(4deg) rotateY(4deg) rotateZ(0deg) translateZ(200px) translateY(0px) skewX(0deg); }
  @media screen and (max-width: 640px) {
    .camera {
      transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px) translateY(0px) skewX(0deg); } }

.filter {
  position: absolute;
  top: 120px;
  left: 15px;
  width: 75%;
  padding: 8px 0px;
  display: none; }
  @media screen and (max-width: 640px) {
    .filter {
      display: block; } }
  .filter a {
    margin-right: 1em; }

.search {
  position: fixed;
  width: 15%;
  top: 15vh;
  right: 60px;
  display: flex;
  -webkit-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -moz-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  -o-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
  transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
  @media screen and (max-width: 640px) {
    .search {
      top: 80px;
      padding: 0px 15px;
      right: 0px;
      width: 100%; } }
  .search input {
    position: relative;
    width: 100%;
    line-height: 20px;
    border-radius: 20px 0px 0px 20px;
    border: solid 1px #CCC;
    padding: 5px 20px;
    box-sizing: border-box; }
  .search .search-btn {
    width: 30px;
    background-image: url(../images/search-icon.png);
    background-size: 50% auto;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 0px 20px 20px 0px;
    background-color: #FFF; }
  .search.active {
    width: 30%; }
    @media screen and (max-width: 640px) {
      .search.active {
        width: 100%; } }

.ui-autocomplete {
  position: fixed;
  font-size: 14px;
  width: 10%;
  margin: 0px 20px;
  padding-left: 15px;
  list-style: none;
  border-bottom: 1px solid #c5c5c5;
  /*border: 1px solid #c5c5c5;
  background-color: #FFF;*/
  max-height: 111px;
  overflow: auto; }
  .ui-autocomplete li {
    padding: 10px 15px;
    border-left: 1px solid #c5c5c5;
    border-right: 1px solid #c5c5c5;
    background-color: #FFF; }

.ui-helper-hidden-accessible {
  display: none; }

.credential-list {
  position: relative;
  width: 100vw;
  height: 100vh;
  /*display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-content: flex-start;
  transform: scale(1.5);*/
  transform-style: preserve-3d;
  transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px) translateY(0px) skewX(0deg); }
  @media screen and (max-width: 640px) {
    .credential-list {
      padding: 180px 0px;
      height: auto; } }
  .credential-list .credential-holder {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start; }
  .credential-list a {
    position: relative;
    left: 0%;
    width: 10%;
    /*height: 20%;*/
    display: inline-block;
    padding: 10px;
    box-sizing: border-box;
    transform-style: preserve-3d;
    user-select: none;
    visibility: hidden; }
    @media screen and (max-width: 640px) {
      .credential-list a {
        width: 33%; } }
    .credential-list a:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: calc(100% - 20px);
      height: calc(100% - 20px);
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.4);
      filter: blur(2px); }
    .credential-list a img {
      position: relative;
      width: 100%;
      /*height: 100%;*/
      z-index: 1;
      box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
      transform: translateZ(5px);
      -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
    .credential-list a:hover {
      transform: scale(1) rotateZ(0deg) rotateY(0deg) rotateX(0deg); }
      .credential-list a:hover img {
        transform: translateZ(20px) rotateX(0deg) rotateY(0deg); }
    .credential-list a.anime {
      visibility: visible;
      -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s;
      transition: all 1s cubic-bezier(0.77, 0, 0.175, 1) 0s; }
    .credential-list a.hide {
      top: 50%;
      left: 50%;
      width: 0%;
      height: 0%;
      padding: 0px;
      /*transform: scale(0);*/ }

#news-container {
  position: relative;
  width: 100%;
  margin: auto;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  /*align-items: start;*/
  grid-auto-flow: row dense;
  gap: 20px;
  padding: 120px 60px;
  /*column-count: 5;
  column-gap: 20px;*/ }
  @media screen and (max-width: 640px) {
    #news-container {
      padding: 120px 15px;
      grid-template-columns: repeat(1, 1fr); } }
  #news-container iframe {
    min-height: 600px; }
  #news-container > a, #news-container > div, #news-container > iframe {
    position: relative;
    width: 100%;
    /*margin: 20px 0;*/
    display: inline-block;
    box-sizing: border-box;
    overflow: hidden;
    border-radius: 10px;
    color: #000;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 15px;
    box-sizing: border-box;
    -webkit-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -moz-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    -o-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
    		/*&:nth-child(2n){
    			width: 25%;
    		}
    
    		&:nth-child(3n){
    			width: 25%;
    		}
    
    		&:nth-child(4n){
    			width: 25%;
    		}
    
    		&:nth-child(5n){
    			width: 25%;
    		}*/ }
    #news-container > a:first-child, #news-container > div:first-child, #news-container > iframe:first-child {
      grid-column-end: span 2;
      grid-row-end: span 2; }
      @media screen and (max-width: 640px) {
        #news-container > a:first-child, #news-container > div:first-child, #news-container > iframe:first-child {
          grid-column-end: span 1;
          grid-row-end: span 1; } }
    #news-container > a:nth-child(5n), #news-container > div:nth-child(5n), #news-container > iframe:nth-child(5n) {
      grid-column-end: span 2; }
      @media screen and (max-width: 640px) {
        #news-container > a:nth-child(5n), #news-container > div:nth-child(5n), #news-container > iframe:nth-child(5n) {
          grid-column-end: span 1;
          grid-row-end: span 1; } }
    #news-container > a:hover, #news-container > div:hover, #news-container > iframe:hover {
      transform: scale(1.1);
      z-index: 5;
      filter: drop-shadow(2px 4px 6px #000); }
    #news-container > a p, #news-container > div p, #news-container > iframe p {
      margin: 1em auto;
      	/*display: inline-block;
          width: 300px;
          height: 66px;
          white-space: nowrap;
          overflow: hidden !important;
          text-overflow: ellipsis;*/
      display: -webkit-box;
      overflow: hidden;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical; }
    #news-container > a .more, #news-container > div .more, #news-container > iframe .more {
      color: #72253d;
      text-decoration: underline; }
    #news-container > a .date, #news-container > div .date, #news-container > iframe .date {
      font-weight: 500; }
  #news-container .img-mask {
    position: relative;
    overflow: hidden; }
  #news-container h3 {
    margin: 10px auto; }

#news-details {
  position: relative;
  padding: 120px 60px; }
  @media screen and (max-width: 640px) {
    #news-details {
      padding: 0px; } }
  #news-details a {
    color: #000;
    text-decoration: underline; }
  #news-details .news-title {
    position: relative;
    top: 10vh;
    width: 60%;
    color: #FFF;
    mix-blend-mode: difference; }
    #news-details .news-title-2 {
      max-width: 900px;
      margin: auto;
      margin-bottom: 60px; }
      @media screen and (max-width: 640px) {
        #news-details .news-title-2 {
          padding: 0px 15px;
          margin-bottom: 30px; } }
  #news-details .news-content {
    position: relative;
    width: 40%;
    margin: 120px 0; }
    #news-details .news-content p {
      margin: 2em 0; }
    #news-details .news-content-2 {
      position: relative;
      max-width: 900px;
      margin: auto; }
      @media screen and (max-width: 640px) {
        #news-details .news-content-2 {
          padding: 0px 15px;
          padding-bottom: 120px; } }
      #news-details .news-content-2 p {
        margin: 2em 0; }

.news-banner {
  position: fixed;
  left: 50%;
  width: 40%;
  height: 100vh;
  background-size: cover;
  border-radius: 20px; }
  .news-banner-2 {
    position: relative;
    width: 100%;
    max-width: 900px;
    height: 70vh;
    margin: auto;
    margin-bottom: 50px;
    background-size: cover; }
    @media screen and (max-width: 640px) {
      .news-banner-2 {
        height: 50vh;
        background-position: center;
        margin-bottom: 30px; } }

.awards {
  position: relative;
  width: 100%; }
  .awards__top-banner {
    position: relative;
    width: 100%;
    height: 100vh;
    background-image: url(../images/awards2022/bg.png);
    background-size: cover;
    background-position: center bottom;
    overflow: hidden; }
    .awards__top-banner:nth-child(2) {
    background-image: url(../images/awards2021/top-bg.png);
  }
    @media screen and (max-width: 640px) {
      .awards__top-banner {
        height: auto;
        padding-top: 130px;
        padding-bottom: 30px;
        text-align: center; } }
    .awards__top-banner:after {
      content: '';
      position: absolute;
      width: 100%;
      height: 15px;
      left: 0%;
      bottom: 0%;
      background-image: url(../images/awards/gold-line.jpg);
      background-size: contain;
    }
    .awards__top-banner:before {
      content: '';
      position: absolute;
      width: 100%;
      height: 50%;
      left: 0%;
      top: 50%;
      /* background-color: rgba(110, 110, 110, 0.2); */
      transform: translateY(40%); }
  .awards__top-title {
    position: absolute;
    width: 40%;
    max-width: 800px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -250%);
    z-index: 10; }
    @media screen and (max-width: 640px) {
      .awards__top-title {
        position: relative;
        top: auto;
        left: auto;
        width: 80%;
        transform: translate(0%, 0%); } }
  .awards__top-awards {
    position: absolute;
    width: 86%;
    left: 7%;
    top: 50%;
    transform: translateY(-46%);
    }
    @media screen and (max-width: 640px) {
      .awards__top-awards {
        position: relative;
        width: 90%;
        transform: translateY(0%); } }
  .awards__top-name {
    position: absolute;
    width: 100%;
    left: 0%;
    bottom: 3%;
    text-align: center; }
    @media screen and (max-width: 640px) {
      .awards__top-name {
        position: relative; } }
    .awards__top-name p {
      padding: .1em 2em;
      border-right: solid 1px #999;
      display: inline-block; }
      @media screen and (max-width: 640px) {
        .awards__top-name p {
          border-right: none; } }
      .awards__top-name p:last-child {
        border-right: none; }
  .awards__content {
    position: relative;
    padding: 0px 60px;
    padding-bottom: 100px;
    /* background-color: #173c50; */
    display: flex;
    flex-wrap: wrap;}
    @media screen and (max-width: 640px) {
      .awards__content {
        padding: 0px 15px;
        flex-direction: column-reverse; } }
    .awards__content .txt {
      width: 38%;
      box-sizing: border-box;
      margin-left: 2%;
      margin-top: 1%;
       }
      @media screen and (max-width: 640px) {
        .awards__content .txt {
          width: 100%;
          padding-right: 0px;
          margin: 2em 0px; } }
    .awards__content .video {
      position: relative;
      width: 60%;
      /* margin-left: 5%; */
    }
    .awards__content .video .web {
      color: white;
    }
    @media screen and (max-width: 640px) {
        .awards__content .video {
          width: 100%;
          margin-top: 0px;
          margin: 2em 0px; } }
    .awards__content video {
      position: absolute;
      z-index: 2;
      width: 86.5%;
      top: 7%;
      left: 6.2%;}
  .awards__sections {
    position: relative;
    width: 100%;
    background-color: #173c50;
    background-size: cover;
    background-position: top left;
    background-attachment: fixed;
    background-repeat:repeat-x; }
    .awards__sections:before {
      content: '';
      position: absolute;
      width: 100%;
      height: 15px;
      left: 0%;
      top: 0%;
      background-image: url(../images/awards/gold-line.jpg);
      background-size: contain; }
      @media screen and (max-width: 640px) {
        .awards__sections:before {
          height: 5px; } }
    .awards__sections:nth-child(4) {
      background-image: url(../images/awards2021/cu_bg.png); }
      .awards__sections:nth-child(4) .name {
        background: linear-gradient(90deg, rgba(23, 60, 80, 0.7) 35%, rgba(255, 255, 255, 0) 60%); }
    .awards__sections:nth-child(3) {
      background-attachment: scroll;
      background-repeat:no-repeat;
      background-color: #d9d9d9; 
    }
      .awards__sections:nth-child(3) .name {
        background: linear-gradient(90deg, rgba(143, 131, 123, 0.7) 35%, rgba(255, 255, 255, 0) 60%); }
        .awards__sections:nth-child(3) .awards__content {
          background: url(../images/awards2021/ct_bg.png) no-repeat left top,
          linear-gradient(90deg, rgba(60, 90, 115, 1) 0%, rgba(20, 50, 70, 1) 100%);
          background-size: contain;
        }
      .awards__sections:nth-child(3) .awards__content {
        /* margin: 0 12.5%; */
        padding-top: 3rem;
      }
      .awards__sections:nth-child(4) .awards__content {
        /* margin: 0 12.5%; */
        padding-top: 3rem;
      }
      .awards__sections:nth-child(4) .row  {
        width: 120%;
      }
      
      .awards__sections:nth-child(4) .row img {
        width: 44%;
        margin: 3% 1%;
      }
      .awards__sections:nth-child(4) .awards__content .txt .nomain img {
        max-width: 130%;
      }
      .awards__sections:nth-child(4) .awards__content .video {
        /* margin: 0 12.5%; */
        max-width: 57%;
        margin-left: 3%;
        margin-top: 7%;
      }
      @media screen and (max-width: 640px) {
        .awards__sections:nth-child(4) .awards__content .video {
          max-width: 100%;
          margin-left: 0%;
          margin-top: 2%;
        }
        .awards__sections:nth-child(4) .awards__content .txt .nomain img {
          max-width: 100%;
        }
        .awards__sections:nth-child(4) .row  {
          width: 100%;
        }
      }
    .awards__sections:nth-child(5) .txt{
      width: 50%;
      margin-left: 0;
    }
    .awards__sections:nth-child(5) .row img{
      max-width: 34%;
    }
    .awards__sections:nth-child(5) {
      background-image: url(../images/awards2021/reit_bg.png);
      background-attachment: fixed;
      background-color: #616160; }
      .awards__sections:nth-child(5) .title {
        padding: 4em 0px; }
        @media screen and (max-width: 640px) {
          .awards__sections:nth-child(5) .title {
            padding-top: 15em;
            padding-bottom: 0px;
            margin-left: 15px;
            width: 80%; } }
      .awards__sections:nth-child(5) .name {
        width: 40%;
        background: linear-gradient(90deg, rgba(202, 186, 159, 0.7) 35%, rgba(255, 255, 255, 0) 80%); }
        @media screen and (max-width: 640px) {
          .awards__sections:nth-child(5) .name {
            width: 100%; } }
      .awards__sections:nth-child(5) .awards__content {
        background-color: transparent;
        padding: 5% 0px;
        padding-bottom: 100px;
        align-items: center; }
        @media screen and (max-width: 640px) {
          .awards__sections:nth-child(5) .awards__content {
            flex-direction: column; } }
      .awards__sections:nth-child(5) .txt {
        /* width: 60%; */
        display: flex;
        flex-wrap: wrap;
        align-items: center; }
        @media screen and (max-width: 640px) {
          .awards__sections:nth-child(5) .txt {
            width: 100%;
            padding: 0px 15px; } }
      .awards__sections:nth-child(5) .nomain {
        width: 100%;
        text-align: center;
        border: none;
        margin-right: 0%;
        display: flex;
        align-items: center;
        justify-content: space-around; }
        .awards__sections:nth-child(5) .nomain:before {
          content: '';
          width: 15%;
          display: block;
          border-top: solid 1px #caba9f; }
        .awards__sections:nth-child(5) .nomain:after {
          content: '';
          width: 15%;
          display: block;
          border-top: solid 1px #caba9f; }
      .awards__sections:nth-child(5) .row {
        /* width: 50%; */
        display: flex; }
        .awards__sections:nth-child(5) .video {
          width: 45%;
        }
        @media screen and (max-width: 640px) {
          .awards__sections:nth-child(5) .video {
            width: 100%;
            margin-bottom: 0;
          }
          .awards__sections:nth-child(5) .row {
            width: 100%; } }
        .awards__sections:nth-child(5) .row img {
          margin-right: auto; }
    .awards__sections .title {
      position: relative;
      width: 90%;
      margin-left: 5%;
      padding-top: 6em;
      padding-bottom: 2em;
      display: inline-block; }
      @media screen and (max-width: 640px) {
        .awards__sections .title {
          padding-top: 10em;
          margin-left: 15px;
          width: 80%; } }
    .awards__sections .name {
      color: #FFF;
      padding: 10px 0px;
      padding-left: 60px; }
      @media screen and (max-width: 640px) {
        .awards__sections .name {
          padding-left: 15px; } }
    .awards__sections .nomain {
      padding: 10px 0px;
      color: #caba9f;
      /* border-top: solid 1px #caba9f;
      border-bottom: solid 1px #caba9f;  */
    }
    .awards__sections .row {
      position: relative;
      width: 100%;
      padding: 20px 0px;
      display: inline-block; }
      @media screen and (max-width: 640px) {
        .awards__sections .row {
          padding: 10px 0px; } }
      .awards__sections .row img {
        width: 50%;
        }
        @media screen and (max-width: 640px) {
          .awards__sections .row img {
            width: 50%;
            height: auto;
            /* margin-right: 10px;  */
          } }
/* ------------- awards 2022 ---------------- */
.awards__2022__content{
  max-width: 1024px;
  margin: 0 auto;
}
.awards__2022__awards{
  margin-top: 10vh;
  height: 98vh;
}
.awards__2022__text {
  position: absolute;
  margin-top: 20vh;
  margin-left: 4%;
  height: 68vh;
}

@media screen and (max-width: 1024px) {
  .awards__2022__awards {
    height: auto;
    max-width: 50vw;
    margin-left: 7vw;
    margin-top: 13vw;
  }
  
  .awards__2022__text {
    height: auto;
    margin-top: 18vw;
    max-width: 34vw;
    margin-left: 2vw;
  }
}
@media screen and (max-width: 640px) {
  .awards__2022__awards {
    max-width: 80vw;
    margin: 0px;
  }
  
  .awards__2022__text {
    position: relative;
    max-width: 80vw;
    margin: 8vh 0;
  }
}
/* ------------- awards 2022 ---------------- */

/* END Style ring-design used  */
/* ------------------------------------------ */
.title--landing, .landing .lead-in {
  font-family: "FreightBig", sans-serif;
  font-size: 80px; }
  @media screen and (max-width: 768px) {
    .title--landing, .landing .lead-in {
      font-size: 50px; } }
.title--extralarge, .home-title, #news-details .news-title {
  font-family: "Montserrat", sans-serif;
  font-size: 100px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--extralarge, .home-title, #news-details .news-title {
      font-size: 50px; } }
.title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 60px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--large, .section-slide .slide-title, .menu .main-menu ul a, .about-container__whois .content-txt h2, #news-details .news-title-2 {
      font-size: 40px; } }
.title--medium, .home-menu a, .slide .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 35px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium, .home-menu a, .slide .project-title {
      font-size: 22px; } }
.title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 26px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--medium-small, .about-container .txt h3, #news-container h3, .awards__sections .nomain {
      font-size: 26px; }
      .title--medium-small br, .about-container .txt h3 br, #news-container h3 br, .awards__sections .nomain br {
        display: none; } }
.title--small, .project-title {
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  letter-spacing: 0.1em; }
  @media screen and (max-width: 768px) {
    .title--small, .project-title {
      font-size: 16px; } }

.content--common, #news-details .news-content, #news-details .news-content-2 {
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 25px;
  /*letter-spacing: 0.1em;*/ }
.content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 1.8em;
  /*letter-spacing: 0.1em;*/ }
  @media screen and (max-width: 768px) {
    .content--sm, #footer, .filter, .search input, .awards__sections .name, .project-info .more-info {
      font-size: 12px; } }
.content--xsm {
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  /*letter-spacing: 0.1em;*/ }

/* ------------------------------------------ */
.project-details {
  position: relative;
  padding-top: 105px;
  text-align: center;
  opacity: 0;
  animation: fadeIn 0.8s;
  animation-delay: 0.8s;
  animation-fill-mode: forwards; }
  @media screen and (max-width: 640px) {
    .project-details {
      position: absolute;
      top: 100px;
      left: 0px;
      width: 100%;
      padding-top: 0px; } }

.project-title {
  color: #FFF;
  font-weight: 300; }
  @media screen and (max-width: 640px) {
    .project-title {
      width: 90%;
      margin: auto; } }

.video-holder {
  position: relative;
  width: 70vw;
  height: 70vh;
  margin: 30px auto; }
  @media screen and (max-width: 640px) {
    .video-holder {
      width: 90%;
      height: 90vw;
      margin: auto; } }
  .video-holder iframe {
    width: 100%;
    height: 100%; }

.project-info {
  position: relative;
  width: 70vw;
  margin: auto;
  padding-bottom: 35px;
  display: inline-block;
  color: #FFF; }
  @media screen and (max-width: 640px) {
    .project-info {
      width: 90%; } }
  .project-info h2 {
    /*@extend .content--sm;*/
    color: #FFF;
    font-weight: 300;
    margin: 16px auto; }
  .project-info p {
    /*@extend .content--xsm;*/
    color: #FFF;
    font-weight: 300;
    margin: 16px auto; }
  .project-info .more-info {
    color: #FFF;
    font-weight: 300;
    margin: 16px auto; }
  .project-info .fade-in {
    animation: fadeIn .8s;
    animation-fill-mode: forwards; }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
/* END Style ring-design used  */
/* ------------------------------------------ */

/*# sourceMappingURL=style.css.map */

#footer .footer--col:first-child a {
  display: none;
}


.popup {
  position: absolute;
  height: 100vh;
  width: 100vw;
  z-index: 9999;
  top: 0;
  left: 0;
  background-color: rgba(202, 186, 159, 0.7);
  display: none;
}

.popup img {
  margin: 25vh auto;
  display: block;
  height: 50vh;
}