* {
	margin: 0;
	padding: 0;
	font-size: 100%;
	-webkit-text-size-adjust:none;
}

:focus {
  -moz-outline-style: none;
  outline:0;
}

:-moz-any-link:focus {
  outline: none;
}

img a {outline : none;}

img {
	border: none;
	border-style: none;
}

table {
	border: none;
	empty-cells: show;
}

th,td {
	border: none;
	text-align: left;
	vertical-align: top;
	font-weight: normal;
}

.clear {
	margin-bottom:30px;
	clear:both;
}

.buttons {
	cursor: pointer;
}

body {
	width:100%;
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-color:#000;
	-webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html, body {
	height: 100%;
}

a {
	outline:0 none !important;
	border:none;
	text-decoration:none;
	border-style: none;
}

*:focus { 
	/*outline: 0 !important;*/
	border: none;
}

a:hover {
	text-decoration:none;
}
*:active {
	/*outline: none !important;*/
	/*border: none;*/
}
.clear {
	width:100%;
	clear:both;
}