<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WeChatController extends Controller
{
    /**
     * 企业微信回调URL验证
     * 
     * @param Request $request
     * @return string
     */
    public function callback(Request $request)
    {
        // 获取企业微信验证参数
        $msg_signature = $request->get('msg_signature');
        $timestamp = $request->get('timestamp');
        $nonce = $request->get('nonce');
        $echostr = $request->get('echostr');

        // 记录日志

        Log::info('企业微信回调验证', [
            'msg_signature' => $msg_signature,
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'echostr' => $echostr,
            'method' => $request->method()
        ]);

        // 如果是GET请求，进行URL验证
        if ($request->isMethod('GET')) {
            return $this->verifyUrl($msg_signature, $timestamp, $nonce, $echostr);
        }

        // 如果是POST请求，处理消息推送
        if ($request->isMethod('POST')) {
            return $this->handleMessage($request);
        }

        return response('Method not allowed', 405);
    }

    /**
     * 验证URL有效性
     * 
     * @param string $msg_signature
     * @param string $timestamp
     * @param string $nonce
     * @param string $echostr
     * @return string
     */
    private function verifyUrl($msg_signature, $timestamp, $nonce, $echostr)
    {
        // 从环境变量获取Token
        $token = config('wechat.token');
        
        if (empty($token)) {
            Log::error('企业微信Token未配置');
            return response('Token not configured', 500);
        }

        // 验证签名
        if ($this->verifySignature($token, $timestamp, $nonce, $echostr, $msg_signature)) {
            Log::info('企业微信URL验证成功', [
                'echostr' => $echostr
            ]);
            // 验证成功，返回echostr
            return $echostr;
        } else {
            Log::error('企业微信URL验证失败');
            return response('Verification failed', 403);
        }
    }

    /**
     * 验证签名
     * 
     * @param string $token
     * @param string $timestamp
     * @param string $nonce
     * @param string $echostr
     * @param string $msg_signature
     * @return bool
     */
    private function verifySignature($token, $timestamp, $nonce, $echostr, $msg_signature)
    {
        // 将token、timestamp、nonce、echostr按字典序排序
        $array = [$token, $timestamp, $nonce, $echostr];
        sort($array, SORT_STRING);
        
        // 拼接字符串
        $str = implode('', $array);
        
        // 进行sha1加密
        $signature = sha1($str);
        
        Log::info('签名验证', [
            'calculated_signature' => $signature,
            'received_signature' => $msg_signature,
            'sorted_array' => $array
        ]);
        
        // 比较签名
        return $signature === $msg_signature;
    }

    /**
     * 处理消息推送
     * 
     * @param Request $request
     * @return string
     */
    private function handleMessage(Request $request)
    {
        // TODO: 实现消息处理逻辑
        Log::info('收到企业微信消息推送', [
            'body' => $request->getContent()
        ]);
        
        return 'success';
    }
}
